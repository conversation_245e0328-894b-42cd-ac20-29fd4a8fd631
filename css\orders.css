/* Orders CSS */

/* Order Filters */
.order-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  min-width: 150px;
}

.filter-group select:focus,
.filter-group input:focus {
  border-color: #4caf50;
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.search-group {
  position: relative;
  flex-grow: 1;
  max-width: 300px;
}

.search-group input {
  width: 100%;
  padding-right: 40px;
}

.search-group button {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 40px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  transition: color 0.2s;
}

.search-group button:hover {
  color: #4caf50;
}

/* Orders List */
.orders-list {
  margin-bottom: 30px;
  min-height: 300px;
}

.order-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  transition: transform 0.2s, box-shadow 0.2s;
  overflow: hidden;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.order-id {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.order-date {
  color: #666;
  font-size: 14px;
}

.order-status {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-processing {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-shipping {
  background-color: #d4edda;
  color: #155724;
}

.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.order-body {
  padding: 15px 20px;
}

.order-products {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  overflow-x: auto;
  padding-bottom: 5px;
}

.order-product {
  flex: 0 0 60px;
  position: relative;
}

.order-product img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #eee;
}

.product-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #4caf50;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-total {
  font-weight: 600;
  font-size: 18px;
  color: #333;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.order-actions button {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.btn-view-order {
  background-color: #4caf50;
  color: white;
}

.btn-view-order:hover {
  background-color: #3d8b40;
}

.btn-track-order {
  background-color: #2196f3;
  color: white;
}

.btn-track-order:hover {
  background-color: #0b7dda;
}

.btn-cancel-order {
  background-color: #f44336;
  color: white;
}

.btn-cancel-order:hover {
  background-color: #d32f2f;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 60px;
  color: #ccc;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 10px;
}

.empty-state p {
  color: #666;
  margin-bottom: 20px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.pagination button {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  color: #333;
}

.pagination button:hover:not([disabled]) {
  background-color: #f1f1f1;
}

.pagination button.active {
  background-color: #4caf50;
  color: white;
}

.pagination button[disabled] {
  color: #ccc;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  gap: 5px;
  margin: 0 10px;
}

/* Order Detail Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
}

.modal-content {
  background-color: #fff;
  border-radius: 8px;
  max-width: 900px;
  margin: 30px auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
  animation: modalFadeIn 0.3s;
}

.modal-sm {
  max-width: 500px;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  font-size: 20px;
  color: #333;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.modal-close:hover {
  color: #f44336;
}

.modal-body {
  padding: 20px;
}

/* Order Detail Styles */
.order-detail-container {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.order-info-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.order-info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.order-info-col {
  flex: 1;
  min-width: 250px;
}

.order-info-col h4 {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ddd;
}

.order-info-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.order-info-list li {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.order-info-list li span:first-child {
  color: #666;
}

.address-info {
  line-height: 1.6;
}

/* Order Timeline */
.order-timeline {
  margin-top: 10px;
}

.order-timeline h4 {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
}

.timeline-container {
  position: relative;
  padding-left: 30px;
}

.timeline-container::before {
  content: "";
  position: absolute;
  left: 7px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #ddd;
}

.timeline-item {
  position: relative;
  padding-bottom: 20px;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-item::before {
  content: "";
  position: absolute;
  left: -30px;
  top: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #4caf50;
}

.timeline-item.active::before {
  background-color: #4caf50;
}

.timeline-item.pending::before {
  border-color: #ffc107;
}

.timeline-item.cancelled::before {
  border-color: #f44336;
  background-color: #f44336;
}

.timeline-content {
  padding-left: 10px;
}

.timeline-date {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.timeline-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.timeline-description {
  font-size: 14px;
  color: #666;
}

/* Order Items */
.order-items-section {
  margin-top: 10px;
}

.order-items-section h4 {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
}

.order-items-table {
  width: 100%;
  border-collapse: collapse;
}

.order-items-header {
  display: flex;
  background-color: #f8f9fa;
  padding: 10px 15px;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  color: #333;
}

.item-col {
  padding: 10px;
}

.item-product {
  flex: 3;
}

.item-price,
.item-quantity,
.item-total {
  flex: 1;
  text-align: center;
}

.order-item {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 15px;
}

.order-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 60px;
  height: 60px;
  margin-right: 15px;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #eee;
}

.item-details {
  flex: 1;
}

.item-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.item-variant {
  font-size: 12px;
  color: #666;
}

/* Order Summary */
.order-summary-section {
  margin-top: 20px;
}

.order-summary-section h4 {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
}

.order-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  max-width: 400px;
  margin-left: auto;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.summary-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.summary-row.total {
  font-weight: 600;
  font-size: 18px;
  color: #333;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ddd;
}

/* Order Actions */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.order-actions .btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-actions .btn i {
  font-size: 16px;
}

.btn-primary {
  background-color: #4caf50;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #3d8b40;
}

.btn-secondary {
  background-color: #f1f1f1;
  color: #333;
  border: 1px solid #ddd;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-danger {
  background-color: #f44336;
  color: white;
  border: none;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

/* Cancel Order Modal */
.text-warning {
  color: #856404;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .order-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
  }

  .filter-group select,
  .filter-group input {
    width: 100%;
    min-width: unset;
  }

  .search-group {
    max-width: 100%;
  }

  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .order-summary {
    flex-direction: column;
    gap: 15px;
  }

  .order-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .order-info-row {
    flex-direction: column;
    gap: 20px;
  }

  .order-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .modal-content {
    margin: 10px;
    width: calc(100% - 20px);
  }
}

@media (max-width: 480px) {
  .order-products {
    justify-content: center;
  }

  .order-items-header {
    display: none;
  }

  .order-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 10px;
  }

  .item-image {
    margin-right: 0;
  }

  .item-col {
    flex: 1 !important;
    text-align: center !important;
  }

  .order-summary {
    max-width: 100%;
  }
}
