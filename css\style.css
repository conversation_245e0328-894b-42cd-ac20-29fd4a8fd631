/* Main Styles for ElectroDrive Website */


/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Roboto", sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f9f9f9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

a {
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 50px;
  position: relative;
  color: #222;
}

.section-title:after {
  content: "";
  display: block;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #4caf50, #2196f3);
  margin: 15px auto 0;
  border-radius: 2px;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 30px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
}

.btn-primary {
  background: linear-gradient(90deg, #4caf50, #2196f3);
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(90deg, #43a047, #1e88e5);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.btn-secondary {
  background-color: #333;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background-color: #222;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid #4caf50;
  color: #4caf50;
}

.btn-outline:hover {
  background-color: #4caf50;
  color: white;
  transform: translateY(-2px);
}

/* Header */
header {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 15px 0;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: 1.8rem;
  background: linear-gradient(90deg, #4caf50, #2196f3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.menu {
  display: flex;
}

.menu li {
  margin: 0 15px;
}

.menu a {
  font-weight: 500;
  position: relative;
  padding: 5px 0;
}

.menu a:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #4caf50, #2196f3);
  transition: width 0.3s ease;
}

.menu a:hover:after,
.menu a.active:after {
  width: 100%;
}

.header-icons {
  display: flex;
  align-items: center;
}

.header-icons a {
  margin-left: 20px;
  font-size: 1.2rem;
  color: #555;
}

.header-icons a:hover {
  color: #4caf50;
}

.cart-icon {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #4caf50;
  color: white;
  font-size: 0.7rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.mobile-menu-btn span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: #333;
  margin: 2px 0;
  transition: all 0.3s ease;
}

/* Hero Section với hình nền đẹp */
.hero {
  background: linear-gradient(rgba(60, 60, 60, 0.6), rgba(60, 60, 60, 0.6)), url('images/car1.jpg') center/cover no-repeat;
  color: #fff;
  padding: 100px 0;
  text-align: center;
}
.hero-content h1 {
  font-size: 3.5rem;
  margin-bottom: 20px;
  font-weight: 700;
  color: #fff;
  text-shadow: 0 2px 16px rgba(0,0,0,0.25);
}
.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  color: #f3f3f3;
  text-shadow: 0 1px 8px rgba(0,0,0,0.18);
}
.hero .btn-primary {
  font-size: 1.1rem;
  padding: 16px 40px;
  border-radius: 30px;
  box-shadow: 0 8px 32px rgba(30,203,123,0.18);
  background: linear-gradient(90deg, #1ecb7b, #1ea7cb);
  color: #fff;
  border: none;
  transition: background 0.2s, box-shadow 0.2s;
}
.hero .btn-primary:hover {
  background: linear-gradient(90deg, #1ea7cb, #1ecb7b);
  box-shadow: 0 12px 40px rgba(30,203,123,0.28);
}
@media (max-width: 900px) {
  .hero {
    padding: 80px 0;
  }
  .hero-content h1 {
    font-size: 2.2rem;
  }
}

/* Featured Products */
.featured-products {
  padding: 80px 0;
  background-color: white;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

.product-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  background: linear-gradient(90deg, #4caf50, #2196f3);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.product-info {
  padding: 20px;
}

.product-info h3 {
  font-size: 1.3rem;
  margin-bottom: 5px;
}

.product-category {
  color: #777;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.product-features {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.product-features span {
  display: flex;
  align-items: center;
}

.product-features i {
  margin-right: 5px;
  color: #4caf50;
}

.product-price {
  margin-bottom: 15px;
}

.price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
}

.old-price {
  text-decoration: line-through;
  color: #999;
  margin-right: 10px;
  font-size: 1rem;
}

.view-all {
  text-align: center;
  margin-top: 50px;
}

/* Why Choose Us */
.why-choose-us {
  padding: 80px 0;
  background-color: #f5f5f5;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.1),
    rgba(33, 150, 243, 0.1)
  );
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 20px;
}

.feature-icon i {
  font-size: 30px;
  background: linear-gradient(90deg, #4caf50, #2196f3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.feature-card h3 {
  margin-bottom: 15px;
  font-size: 1.3rem;
}

/* Testimonials */
.testimonials {
  padding: 80px 0;
  background-color: white;
}

.testimonial-slider {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
}

.testimonial-slide {
  display: none;
}

.testimonial-slide.active {
  display: block;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.testimonial-content {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonial-image {
  flex: 0 0 120px;
  margin-right: 30px;
}

.testimonial-image img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 5px solid white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.testimonial-text {
  flex: 1;
}

.testimonial-text p {
  font-style: italic;
  margin-bottom: 20px;
  font-size: 1.1rem;
  line-height: 1.7;
}

.testimonial-author h4 {
  font-size: 1.2rem;
  margin-bottom: 5px;
  color: #333;
}

.testimonial-author p {
  font-style: normal;
  color: #777;
  margin-bottom: 0;
  font-size: 0.9rem;
}

.testimonial-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.prev-btn,
.next-btn {
  background-color: transparent;
  border: none;
  font-size: 1.5rem;
  color: #555;
  cursor: pointer;
  padding: 5px 10px;
  transition: color 0.3s ease;
}

.prev-btn:hover,
.next-btn:hover {
  color: #4caf50;
}

.testimonial-dots {
  display: flex;
  margin: 0 20px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ddd;
  margin: 0 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dot.active {
  background-color: #4caf50;
}

/* Newsletter */
.newsletter {
  padding: 60px 0;
  background: linear-gradient(90deg, #4caf50, #2196f3);
  color: white;
}

.newsletter-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-content h2 {
  font-size: 2rem;
  margin-bottom: 15px;
}

.newsletter-content p {
  margin-bottom: 30px;
  font-size: 1.1rem;
}

.newsletter-form {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-form input {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: 30px 0 0 30px;
  font-size: 1rem;
  outline: none;
}

.newsletter-form button {
  border-radius: 0 30px 30px 0;
  padding: 15px 30px;
  background-color: #333;
  color: white;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.newsletter-form button:hover {
  background-color: #222;
}

/* Contact Section */
.contact {
  padding: 80px 0;
  background-color: #f5f5f5;
}

.contact-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
}

.contact-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
}

.contact-item i {
  font-size: 1.5rem;
  color: #4caf50;
  margin-right: 15px;
  margin-top: 5px;
}

.contact-item h3 {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.contact-form form {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  outline: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #4caf50;
}

.form-group textarea {
  height: 150px;
  resize: vertical;
}

/* Footer */
footer {
  background-color: #222;
  color: #fff;
  padding: 70px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-bottom: 50px;
}

.footer-column h3 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-column h3:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #4caf50, #2196f3);
}

.footer-logo h2 {
  font-size: 1.8rem;
  margin-bottom: 15px;
}

.footer-column p {
  margin-bottom: 20px;
  color: #aaa;
  line-height: 1.7;
}

.social-icons {
  display: flex;
}

.social-icons a {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin-right: 10px;
  transition: all 0.3s ease;
}

.social-icons a:hover {
  background-color: #4caf50;
  transform: translateY(-3px);
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links i {
  margin-right: 8px;
  color: #1ea7cb;
  min-width: 18px;
  text-align: center;
}

.footer-links a {
  display: flex;
  align-items: center;
}

.footer-links a:hover {
  color: #4caf50;
  padding-left: 5px;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: #aaa;
}

.payment-methods {
  display: flex;
}

.payment-methods img {
  height: 30px;
  margin-left: 10px;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: #4caf50;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: #43a047;
  transform: translateY(-5px);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .hero-content h1 {
    font-size: 3rem;
  }

  .contact-wrapper {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: white;
    flex-direction: column;
    padding: 20px 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .menu.active {
    display: flex;
  }

  .menu li {
    margin: 10px 20px;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-content p {
    font-size: 1rem;
  }

  .testimonial-content {
    flex-direction: column;
    text-align: center;
  }

  .testimonial-image {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .payment-methods {
    margin-top: 15px;
  }
}

@media (max-width: 576px) {
  .section-title {
    font-size: 2rem;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .newsletter-form input {
    border-radius: 30px;
    margin-bottom: 10px;
  }

  .newsletter-form button {
    border-radius: 30px;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }
  .fas fa-envelope{
    padding-right: 10px;
  }
}
