document.addEventListener("DOMContentLoaded", function () {
  // Initialize checkout functionality
  initCheckout();
});

/**
 * Initialize checkout functionality
 */
function initCheckout() {
  // Load cart data
  loadCartItems();

  // Initialize form validation
  initFormValidation();

  // Initialize payment method selection
  initPaymentMethods();

  // Initialize location selectors (city, district, ward)
  initLocationSelectors();

  // Initialize place order button
  document
    .querySelector(".place-order-btn")
    .addEventListener("click", function (e) {
      e.preventDefault();
      placeOrder();
    });
}

/**
 * Load cart items from localStorage and display in order summary
 */
function loadCartItems() {
  // Get cart items from localStorage
  const cart = JSON.parse(localStorage.getItem("cart")) || [];

  // If cart is empty, redirect to cart page
  if (cart.length === 0) {
    window.location.href = "cart.html";
    return;
  }

  // Get order items container
  const orderItemsContainer = document.getElementById("order-items");
  const template = document.getElementById("order-item-template");

  // Clear container
  orderItemsContainer.innerHTML = "";

  // Variables for order summary
  let subtotal = 0;
  let discount = 0;

  // Add each item to the order summary
  cart.forEach((item) => {
    // Clone template
    const orderItem = document.importNode(template.content, true);

    // Set item details
    orderItem.querySelector(".item-name").textContent = item.name;
    orderItem.querySelector(".item-color").textContent =
      item.color || "Mặc định";
    orderItem.querySelector(".quantity-value").textContent = item.quantity;
    orderItem.querySelector(".item-total").textContent = formatCurrency(
      item.price * item.quantity
    );

    // Add to container
    orderItemsContainer.appendChild(orderItem);

    // Update subtotal
    subtotal += item.price * item.quantity;
  });

  // Get discount from localStorage
  const appliedDiscount = localStorage.getItem("cartDiscount") || 0;
  discount = parseFloat(appliedDiscount);

  // Calculate shipping fee (simplified example)
  const shippingFee = subtotal > 50000000 ? 0 : 500000; // Free shipping for orders over 50M VND

  // Update order summary
  document.getElementById("order-subtotal").textContent =
    formatCurrency(subtotal);
  document.getElementById("order-shipping").textContent =
    formatCurrency(shippingFee);
  document.getElementById("order-discount").textContent =
    "-" + formatCurrency(discount);
  document.getElementById("order-total").textContent = formatCurrency(
    subtotal + shippingFee - discount
  );

  // Store order data for submission
  window.orderData = {
    subtotal: subtotal,
    shipping: shippingFee,
    discount: discount,
    total: subtotal + shippingFee - discount,
    items: cart,
  };
}

/**
 * Initialize form validation
 */
function initFormValidation() {
  const form = document.getElementById("checkout-form");

  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Validate customer information
    const fullname = document.getElementById("fullname").value.trim();
    const email = document.getElementById("email").value.trim();
    const phone = document.getElementById("phone").value.trim();

    // Validate shipping information
    const address = document.getElementById("address").value.trim();
    const city = document.getElementById("city").value;
    const district = document.getElementById("district").value;
    const ward = document.getElementById("ward").value;

    // Validate payment method
    const paymentMethod = document.querySelector(
      'input[name="payment_method"]:checked'
    );

    // Check required fields
    let isValid = true;
    let errorMessage = "";

    if (!fullname) {
      isValid = false;
      errorMessage = "Vui lòng nhập họ và tên";
    } else if (!email || !isValidEmail(email)) {
      isValid = false;
      errorMessage = "Vui lòng nhập email hợp lệ";
    } else if (!phone || !isValidPhone(phone)) {
      isValid = false;
      errorMessage = "Vui lòng nhập số điện thoại hợp lệ";
    } else if (!address) {
      isValid = false;
      errorMessage = "Vui lòng nhập địa chỉ";
    } else if (!city) {
      isValid = false;
      errorMessage = "Vui lòng chọn Tỉnh/Thành phố";
    } else if (!district) {
      isValid = false;
      errorMessage = "Vui lòng chọn Quận/Huyện";
    } else if (!ward) {
      isValid = false;
      errorMessage = "Vui lòng chọn Phường/Xã";
    } else if (!paymentMethod) {
      isValid = false;
      errorMessage = "Vui lòng chọn phương thức thanh toán";
    }

    // Validate credit card information if credit card payment is selected
    if (paymentMethod && paymentMethod.value === "credit") {
      const cardNumber = document.getElementById("card-number").value.trim();
      const cardName = document.getElementById("card-name").value.trim();
      const cardExpiry = document.getElementById("card-expiry").value.trim();
      const cardCvv = document.getElementById("card-cvv").value.trim();

      if (!cardNumber || !isValidCardNumber(cardNumber)) {
        isValid = false;
        errorMessage = "Vui lòng nhập số thẻ hợp lệ";
      } else if (!cardName) {
        isValid = false;
        errorMessage = "Vui lòng nhập tên chủ thẻ";
      } else if (!cardExpiry || !isValidCardExpiry(cardExpiry)) {
        isValid = false;
        errorMessage = "Vui lòng nhập ngày hết hạn hợp lệ (MM/YY)";
      } else if (!cardCvv || !isValidCardCvv(cardCvv)) {
        isValid = false;
        errorMessage = "Vui lòng nhập mã bảo mật hợp lệ";
      }
    }

    if (!isValid) {
      showNotification(errorMessage, "error");
      return;
    }

    // If all validations pass, proceed with order placement
    placeOrder();
  });
}

/**
 * Initialize payment method selection
 */
function initPaymentMethods() {
  const paymentMethods = document.querySelectorAll(
    'input[name="payment_method"]'
  );
  const bankDetails = document.querySelector(".bank-details");
  const creditCardForm = document.querySelector(".credit-card-form");

  // Add event listeners to payment method radios
  paymentMethods.forEach((method) => {
    method.addEventListener("change", function () {
      // Hide all payment details first
      bankDetails.style.display = "none";
      creditCardForm.style.display = "none";

      // Show relevant payment details based on selection
      if (this.value === "bank") {
        bankDetails.style.display = "block";
      } else if (this.value === "credit") {
        creditCardForm.style.display = "block";
      }

      // Highlight selected payment method
      document.querySelectorAll(".payment-method").forEach((el) => {
        el.style.borderColor = "#ddd";
      });
      this.closest(".payment-method").style.borderColor = "#4CAF50";
    });
  });

  // Initialize with default selection
  document.getElementById("payment-cod").checked = true;
  document
    .getElementById("payment-cod")
    .closest(".payment-method").style.borderColor = "#4CAF50";
}

/**
 * Initialize location selectors (city, district, ward)
 */
function initLocationSelectors() {
  const citySelect = document.getElementById("city");
  const districtSelect = document.getElementById("district");
  const wardSelect = document.getElementById("ward");

  // Sample data for demonstration
  const locationData = {
    hanoi: {
      name: "Hà Nội",
      districts: {
        badinh: {
          name: "Ba Đình",
          wards: ["Phúc Xá", "Trúc Bạch", "Vĩnh Phúc"],
        },
        caugiay: {
          name: "Cầu Giấy",
          wards: ["Dịch Vọng", "Mai Dịch", "Quan Hoa"],
        },
        dongda: {
          name: "Đống Đa",
          wards: ["Cát Linh", "Văn Miếu", "Quốc Tử Giám"],
        },
      },
    },
    hochiminh: {
      name: "TP. Hồ Chí Minh",
      districts: {
        district1: {
          name: "Quận 1",
          wards: ["Bến Nghé", "Bến Thành", "Đa Kao"],
        },
        district3: {
          name: "Quận 3",
          wards: ["Võ Thị Sáu", "Phường 9", "Phường 10"],
        },
        district7: {
          name: "Quận 7",
          wards: ["Tân Thuận Đông", "Tân Thuận Tây", "Tân Kiểng"],
        },
      },
    },
    danang: {
      name: "Đà Nẵng",
      districts: {
        haichau: {
          name: "Hải Châu",
          wards: ["Hải Châu 1", "Hải Châu 2", "Thạch Thang"],
        },
        thanhhke: {
          name: "Thanh Khê",
          wards: ["Thanh Khê Đông", "Thanh Khê Tây", "Xuân Hà"],
        },
        sontra: {
          name: "Sơn Trà",
          wards: ["An Hải Bắc", "An Hải Đông", "Mân Thái"],
        },
      },
    },
  };

  // City change event
  citySelect.addEventListener("change", function () {
    // Clear district and ward selects
    districtSelect.innerHTML = '<option value="">Chọn Quận/Huyện</option>';
    wardSelect.innerHTML = '<option value="">Chọn Phường/Xã</option>';

    // Disable district and ward selects if no city is selected
    if (!this.value) {
      districtSelect.disabled = true;
      wardSelect.disabled = true;
      return;
    }

    // Enable district select and populate options
    districtSelect.disabled = false;

    // Get districts for selected city
    const districts = locationData[this.value].districts;

    // Add district options
    for (const districtId in districts) {
      const option = document.createElement("option");
      option.value = districtId;
      option.textContent = districts[districtId].name;
      districtSelect.appendChild(option);
    }
  });

  // District change event
  districtSelect.addEventListener("change", function () {
    // Clear ward select
    wardSelect.innerHTML = '<option value="">Chọn Phường/Xã</option>';

    // Disable ward select if no district is selected
    if (!this.value) {
      wardSelect.disabled = true;
      return;
    }

    // Enable ward select and populate options
    wardSelect.disabled = false;

    // Get wards for selected district
    const cityId = citySelect.value;
    const districtId = this.value;
    const wards = locationData[cityId].districts[districtId].wards;

    // Add ward options
    wards.forEach((ward) => {
      const option = document.createElement("option");
      option.value = ward.toLowerCase().replace(/\s+/g, "");
      option.textContent = ward;
      wardSelect.appendChild(option);
    });
  });
}

/**
 * Place order
 */
function placeOrder() {
  // Get form data
  const form = document.getElementById("checkout-form");
  const formData = new FormData(form);
  const orderData = window.orderData;

  // Create order object
  const order = {
    customer: {
      fullname: formData.get("fullname"),
      email: formData.get("email"),
      phone: formData.get("phone"),
      company: formData.get("company"),
    },
    shipping: {
      address: formData.get("address"),
      city: document.getElementById("city").options[
        document.getElementById("city").selectedIndex
      ].text,
      district:
        document.getElementById("district").options[
          document.getElementById("district").selectedIndex
        ].text,
      ward: document.getElementById("ward").options[
        document.getElementById("ward").selectedIndex
      ].text,
      zipcode: formData.get("zipcode"),
      notes: formData.get("notes"),
    },
    payment: {
      method: formData.get("payment_method"),
      // Add payment details based on method
      details:
        formData.get("payment_method") === "credit"
          ? {
              cardNumber: formData.get("card_number"),
              cardName: formData.get("card_name"),
              cardExpiry: formData.get("card_expiry"),
              // Don't store CVV for security reasons
            }
          : {},
    },
    order: orderData,
  };

  // In a real application, you would send this data to a server
  // For this demo, we'll simulate a successful order
  console.log("Order placed:", order);

  // Show loading state
  const placeOrderBtn = document.querySelector(".place-order-btn");
  placeOrderBtn.disabled = true;
  placeOrderBtn.innerHTML =
    '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';

  // Simulate server processing
  setTimeout(function () {
    // Clear cart
    localStorage.removeItem("cart");
    localStorage.removeItem("cartDiscount");

    // Store order in localStorage for demo purposes
    const orders = JSON.parse(localStorage.getItem("orders")) || [];
    order.id = "ORD" + Date.now();
    order.date = new Date().toISOString();
    order.status = "pending";
    orders.push(order);
    localStorage.setItem("orders", JSON.stringify(orders));

    // Show success message and redirect to confirmation page
    showNotification("Đặt hàng thành công! Cảm ơn bạn đã mua hàng.", "success");

    // In a real application, redirect to an order confirmation page
    // For this demo, we'll redirect back to the home page after a delay
    setTimeout(function () {
      window.location.href = "index.html";
    }, 2000);
  }, 2000);
}

/**
 * Show notification
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, error, info)
 */
function showNotification(message, type = "info") {
  // Create notification element if it doesn't exist
  let notification = document.querySelector(".notification");

  if (!notification) {
    notification = document.createElement("div");
    notification.className = "notification";
    document.body.appendChild(notification);

    // Add styles
    notification.style.position = "fixed";
    notification.style.top = "20px";
    notification.style.right = "20px";
    notification.style.padding = "15px 20px";
    notification.style.borderRadius = "4px";
    notification.style.color = "#fff";
    notification.style.fontWeight = "500";
    notification.style.zIndex = "9999";
    notification.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
    notification.style.transition = "all 0.3s ease";
    notification.style.opacity = "0";
    notification.style.transform = "translateY(-20px)";
  }

  // Set notification type
  if (type === "success") {
    notification.style.backgroundColor = "#4CAF50";
  } else if (type === "error") {
    notification.style.backgroundColor = "#F44336";
  } else {
    notification.style.backgroundColor = "#2196F3";
  }

  // Set message
  notification.textContent = message;

  // Show notification
  setTimeout(() => {
    notification.style.opacity = "1";
    notification.style.transform = "translateY(0)";
  }, 10);

  // Hide notification after 3 seconds
  setTimeout(() => {
    notification.style.opacity = "0";
    notification.style.transform = "translateY(-20px)";
  }, 3000);
}

/**
 * Format currency
 * @param {number} amount - Amount to format
 * @returns {string} Formatted currency string
 */
function formatCurrency(amount) {
  return new Intl.NumberFormat("vi-VN", { style: "currency", currency: "VND" })
    .format(amount)
    .replace("₫", "₫");
}

/**
 * Validate email
 * @param {string} email - Email to validate
 * @returns {boolean} True if email is valid
 */
function isValidEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

/**
 * Validate phone number
 * @param {string} phone - Phone number to validate
 * @returns {boolean} True if phone number is valid
 */
function isValidPhone(phone) {
  // Simple validation for Vietnamese phone numbers
  const re = /^(0|\+84)(3|5|7|8|9)\d{8}$/;
  return re.test(phone.replace(/\s+/g, ""));
}

/**
 * Validate credit card number
 * @param {string} cardNumber - Card number to validate
 * @returns {boolean} True if card number is valid
 */
function isValidCardNumber(cardNumber) {
  // Remove spaces and dashes
  const cleanedNumber = cardNumber.replace(/[\s-]/g, "");

  // Check if it contains only digits and has a valid length (13-19 digits)
  return /^\d{13,19}$/.test(cleanedNumber);
}

/**
 * Validate credit card expiry date
 * @param {string} expiry - Expiry date to validate (MM/YY)
 * @returns {boolean} True if expiry date is valid
 */
function isValidCardExpiry(expiry) {
  // Check format (MM/YY)
  if (!/^\d{2}\/\d{2}$/.test(expiry)) {
    return false;
  }

  const [month, year] = expiry.split("/");
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear() % 100; // Get last 2 digits of year
  const currentMonth = currentDate.getMonth() + 1; // January is 0

  // Convert to numbers
  const expiryMonth = parseInt(month, 10);
  const expiryYear = parseInt(year, 10);

  // Check if month is valid (1-12)
  if (expiryMonth < 1 || expiryMonth > 12) {
    return false;
  }

  // Check if expiry date is in the future
  return (
    expiryYear > currentYear ||
    (expiryYear === currentYear && expiryMonth >= currentMonth)
  );
}

/**
 * Validate credit card CVV
 * @param {string} cvv - CVV to validate
 * @returns {boolean} True if CVV is valid
 */
function isValidCardCvv(cvv) {
  // CVV should be 3 or 4 digits
  return /^\d{3,4}$/.test(cvv);
}
