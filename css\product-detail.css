/* Product Detail Styles */

/* Breadcrumb Styles */
.breadcrumb-container {
  background-color: #f8f9fa;
  padding: 15px 0;
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.breadcrumb a {
  color: #555;
  text-decoration: none;
  transition: color 0.3s;
}

.breadcrumb a:hover {
  color: var(--primary-color);
}

.breadcrumb .separator {
  margin: 0 10px;
  color: #ccc;
}

.breadcrumb .current {
  color: var(--primary-color);
  font-weight: 500;
}

/* Product Detail Wrapper */
.product-detail {
  padding: 40px 0 80px;
}

.product-detail-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  margin-bottom: 60px;
}

/* Product Images */
.product-images {
  position: relative;
}

.main-image {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.main-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s;
}

.main-image:hover img {
  transform: scale(1.05);
}

.product-tag {
  position: absolute;
  top: 15px;
  left: 15px;
  background-color: var(--primary-color);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  z-index: 2;
}

.thumbnail-images {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.thumbnail {
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.thumbnail img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

.thumbnail.active {
  border-color: var(--primary-color);
}

.thumbnail:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

/* Product Info */
.product-info {
  display: flex;
  flex-direction: column;
}

.product-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 10px;
  color: #333;
}

.product-category {
  color: #777;
  font-size: 16px;
  margin-bottom: 15px;
}

.product-rating {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.stars {
  color: #ffc107;
  margin-right: 10px;
}

.rating-count {
  color: #777;
  font-size: 14px;
}

.product-price {
  margin-bottom: 25px;
}

.price {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary-color);
}

.old-price {
  font-size: 20px;
  color: #999;
  text-decoration: line-through;
  margin-right: 10px;
}

.product-description {
  margin-bottom: 25px;
  line-height: 1.6;
  color: #555;
}

/* Product Features Highlight */
.product-features-highlight {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
}

.feature {
  display: flex;
  align-items: center;
}

.feature i {
  font-size: 24px;
  color: var(--primary-color);
  margin-right: 15px;
}

.feature-info h4 {
  font-size: 14px;
  margin: 0 0 5px;
  color: #777;
}

.feature-info p {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

/* Product Colors */
.product-colors {
  margin-bottom: 25px;
}

.product-colors h3,
.product-quantity h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.color-options {
  display: flex;
  gap: 15px;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.color-option.active {
  border: 2px solid var(--primary-color);
}

.color-option:hover {
  transform: scale(1.1);
}

.color-name {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  white-space: nowrap;
  color: #555;
  opacity: 0;
  transition: opacity 0.3s;
}

.color-option:hover .color-name {
  opacity: 1;
}

/* Product Quantity */
.product-quantity {
  margin-bottom: 30px;
}

.quantity-selector {
  display: flex;
  align-items: center;
  max-width: 150px;
}

.quantity-btn {
  width: 40px;
  height: 40px;
  background-color: #f1f1f1;
  border: none;
  border-radius: 5px;
  font-size: 18px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.quantity-btn:hover {
  background-color: #e1e1e1;
}

.quantity-input {
  width: 70px;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 5px;
  text-align: center;
  font-size: 16px;
  margin: 0 10px;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

.add-to-cart-btn,
.buy-now-btn {
  padding: 12px 25px;
  font-size: 16px;
  font-weight: 500;
}

.add-to-cart-btn i,
.buy-now-btn i {
  margin-right: 8px;
}

.wishlist-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f8f9fa;
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.wishlist-btn i {
  font-size: 20px;
  color: #777;
  transition: color 0.3s;
}

.wishlist-btn:hover {
  background-color: #fff0f0;
  border-color: #ffcaca;
}

.wishlist-btn:hover i {
  color: #ff5252;
}

.wishlist-btn.active {
  background-color: #fff0f0;
  border-color: #ffcaca;
}

.wishlist-btn.active i {
  color: #ff5252;
  font-weight: 900;
}

/* Product Meta */
.product-meta {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.meta-item {
  margin-bottom: 10px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.meta-item .label {
  color: #777;
  margin-right: 10px;
  min-width: 100px;
}

.meta-item .value,
.meta-item a {
  color: #333;
  font-weight: 500;
}

.meta-item a:hover {
  color: var(--primary-color);
}

.social-share {
  display: flex;
  gap: 10px;
}

.social-share a {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f1f1f1;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.social-share a:hover {
  background-color: var(--primary-color);
}

.social-share a:hover i {
  color: white;
}

.social-share i {
  font-size: 14px;
  color: #555;
  transition: color 0.3s;
}

/* Product Tabs */
.product-tabs {
  margin-bottom: 60px;
}

.tabs-header {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 30px;
}

.tab-btn {
  padding: 15px 30px;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: #777;
  cursor: pointer;
  position: relative;
  transition: color 0.3s;
}

.tab-btn:after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
  transform: scaleX(0);
  transition: transform 0.3s;
}

.tab-btn.active {
  color: var(--primary-color);
}

.tab-btn.active:after {
  transform: scaleX(1);
}

.tab-content {
  display: none;
  animation: fadeIn 0.5s;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Description Tab */
.tab-content h2 {
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
}

.tab-content h3 {
  font-size: 18px;
  margin: 25px 0 15px;
  color: #444;
}

.tab-content p {
  line-height: 1.7;
  margin-bottom: 15px;
  color: #555;
}

/* Specifications Tab */
.specs-group {
  margin-bottom: 30px;
}

.specs-group h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.specs-table {
  width: 100%;
}

.specs-row {
  display: flex;
  border-bottom: 1px solid #f1f1f1;
}

.specs-row:last-child {
  border-bottom: none;
}

.specs-label {
  width: 40%;
  padding: 12px 15px 12px 0;
  color: #777;
  font-weight: 500;
}

.specs-value {
  width: 60%;
  padding: 12px 0;
  color: #333;
}

/* Reviews Tab */
.reviews-summary {
  display: flex;
  gap: 50px;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #eee;
}

.overall-rating {
  text-align: center;
  min-width: 200px;
}

.rating-number {
  font-size: 48px;
  font-weight: 700;
  color: #333;
  line-height: 1;
  margin-bottom: 10px;
}

.rating-stars {
  color: #ffc107;
  font-size: 24px;
  margin-bottom: 10px;
}

.rating-count {
  color: #777;
  font-size: 14px;
}

.rating-bars {
  flex-grow: 1;
}

.rating-bar {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.rating-level {
  width: 60px;
  font-size: 14px;
  color: #777;
}

.bar-container {
  flex-grow: 1;
  height: 8px;
  background-color: #f1f1f1;
  border-radius: 4px;
  margin: 0 15px;
  overflow: hidden;
}

.bar {
  height: 100%;
  background-color: #ffc107;
  border-radius: 4px;
}

.rating-percent {
  width: 40px;
  font-size: 14px;
  color: #777;
  text-align: right;
}

.review-list {
  margin-bottom: 40px;
}

.review-item {
  display: flex;
  gap: 20px;
  padding: 25px 0;
  border-bottom: 1px solid #eee;
}

.reviewer-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.reviewer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.review-content {
  flex-grow: 1;
}

.reviewer-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.review-date {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
}

.review-rating {
  color: #ffc107;
  margin-bottom: 10px;
}

.review-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.review-text {
  line-height: 1.6;
  color: #555;
  margin-bottom: 15px;
}

.review-photos {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.review-photos img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 5px;
  cursor: pointer;
  transition: transform 0.3s;
}

.review-photos img:hover {
  transform: scale(1.05);
}

/* Review Form */
.review-form-container {
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 10px;
}

.review-form-container h3 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
}

.review-form .form-group {
  margin-bottom: 20px;
}

.review-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.rating-select {
  display: flex;
  gap: 5px;
  font-size: 24px;
  color: #ddd;
}

.rating-select i {
  cursor: pointer;
  transition: color 0.2s;
}

.rating-select i:hover,
.rating-select i.active {
  color: #ffc107;
}

.review-form input[type="text"],
.review-form textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.review-form input[type="text"]:focus,
.review-form textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.file-upload {
  position: relative;
  display: inline-block;
}

.file-upload input[type="file"] {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-btn {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background-color: #f1f1f1;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.file-upload-btn:hover {
  background-color: #e1e1e1;
}

.file-upload-btn i {
  margin-right: 8px;
}

/* Related Products */
.related-products {
  margin-top: 80px;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
  text-align: center;
  color: #333;
  position: relative;
  padding-bottom: 15px;
}

.section-title:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .product-detail-wrapper {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .product-features-highlight {
    grid-template-columns: repeat(3, 1fr);
  }

  .reviews-summary {
    flex-direction: column;
    gap: 30px;
  }

  .overall-rating {
    min-width: auto;
  }

  .product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .tabs-header {
    flex-wrap: wrap;
  }

  .tab-btn {
    padding: 12px 20px;
    font-size: 14px;
  }

  .product-features-highlight {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .product-actions {
    flex-wrap: wrap;
  }

  .add-to-cart-btn,
  .buy-now-btn {
    width: calc(50% - 10px);
    padding: 12px 15px;
    font-size: 14px;
  }

  .wishlist-btn {
    margin-top: 15px;
  }

  .specs-label,
  .specs-value {
    padding: 10px 0;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .product-title {
    font-size: 24px;
  }

  .price {
    font-size: 24px;
  }

  .old-price {
    font-size: 18px;
  }

  .thumbnail-images {
    grid-template-columns: repeat(4, 1fr);
  }

  .thumbnail img {
    height: 60px;
  }

  .product-grid {
    grid-template-columns: 1fr;
  }

  .add-to-cart-btn,
  .buy-now-btn {
    width: 100%;
    margin-bottom: 10px;
  }

  .wishlist-btn {
    width: 100%;
    border-radius: 5px;
    height: 45px;
  }

  .review-item {
    flex-direction: column;
    gap: 15px;
  }

  .reviewer-avatar {
    width: 50px;
    height: 50px;
  }
}
