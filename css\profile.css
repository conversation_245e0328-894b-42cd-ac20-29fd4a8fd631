/* Profile Page Styles */

/* Breadcrumb */
.breadcrumb-container {
  background-color: #f8f9fa;
  padding: 15px 0;
  border-bottom: 1px solid #e9ecef;
}

.breadcrumb {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
}

.breadcrumb li {
  font-size: 14px;
  color: #6c757d;
}

.breadcrumb li:not(:last-child)::after {
  content: "/";
  margin: 0 10px;
  color: #adb5bd;
}

.breadcrumb a {
  color: #6c757d;
  text-decoration: none;
}

.breadcrumb a:hover {
  color: #00b794;
}

/* Profile Section */
.profile-section {
  padding: 40px 0 60px;
}

.profile-container {
  display: flex;
  gap: 30px;
}

/* Profile Sidebar */
.profile-sidebar {
  width: 280px;
  flex-shrink: 0;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.user-info {
  padding: 30px 20px;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
}

.user-avatar {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin: 0 auto 15px;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.change-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  background-color: #00b794;
  color: #fff;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.change-avatar-btn:hover {
  background-color: #009d7e;
}

.user-info h3 {
  font-size: 18px;
  margin-bottom: 5px;
  color: #333;
}

.user-info p {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.profile-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.profile-menu li {
  border-bottom: 1px solid #e9ecef;
}

.profile-menu li:last-child {
  border-bottom: none;
}

.profile-menu a {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #495057;
  text-decoration: none;
  transition: all 0.3s;
}

.profile-menu a:hover {
  background-color: #f8f9fa;
  color: #00b794;
}

.profile-menu li.active a {
  background-color: #e8f7f4;
  color: #00b794;
  font-weight: 500;
  border-left: 3px solid #00b794;
}

.profile-menu i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

/* Profile Content */
.profile-content {
  flex: 1;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.profile-header {
  padding: 25px 30px;
  border-bottom: 1px solid #e9ecef;
}

.profile-header h1 {
  font-size: 24px;
  margin: 0 0 5px;
  color: #333;
}

.profile-header p {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.profile-body {
  padding: 30px;
}

/* Form Styles */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
}

.form-group.full-width {
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group .required {
  color: #e74c3c;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="tel"],
.form-group input[type="date"] {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  border-color: #00b794;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 183, 148, 0.2);
}

.form-group input:read-only {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.form-text {
  display: block;
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
}

/* Radio Button Styles */
.radio-group {
  display: flex;
  gap: 20px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-label input[type="radio"] {
  position: absolute;
  opacity: 0;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #ddd;
  margin-right: 8px;
  position: relative;
  transition: all 0.3s;
}

.radio-label:hover .radio-custom {
  border-color: #00b794;
}

.radio-custom::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #00b794;
  transition: all 0.2s;
}

.radio-label input[type="radio"]:checked + .radio-custom {
  border-color: #00b794;
}

.radio-label input[type="radio"]:checked + .radio-custom::after {
  transform: translate(-50%, -50%) scale(1);
}

/* Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 2px solid #ddd;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s;
}

.checkbox-label:hover .checkbox-custom {
  border-color: #00b794;
}

.checkbox-custom::after {
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  color: #fff;
  font-size: 12px;
  transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background-color: #00b794;
  border-color: #00b794;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  transform: translate(-50%, -50%) scale(1);
}

/* Form Divider */
.form-divider {
  height: 1px;
  background-color: #e9ecef;
  margin: 30px 0;
}

/* Password Toggle */
.input-icon-wrapper {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #aaa;
  cursor: pointer;
}

.toggle-password:hover {
  color: #666;
}

/* Password Strength Meter */
.password-strength {
  margin-top: 10px;
}

.strength-meter {
  height: 5px;
  background-color: #eee;
  border-radius: 3px;
  margin-bottom: 5px;
}

.strength-meter-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s, background-color 0.3s;
}

.strength-meter-fill[data-strength="0"] {
  width: 20%;
  background-color: #e74c3c;
}

.strength-meter-fill[data-strength="1"] {
  width: 40%;
  background-color: #e67e22;
}

.strength-meter-fill[data-strength="2"] {
  width: 60%;
  background-color: #f1c40f;
}

.strength-meter-fill[data-strength="3"] {
  width: 80%;
  background-color: #2ecc71;
}

.strength-meter-fill[data-strength="4"] {
  width: 100%;
  background-color: #27ae60;
}

.strength-text {
  font-size: 12px;
  color: #666;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.btn-primary {
  background-color: #00b794;
  color: #fff;
}

.btn-primary:hover {
  background-color: #009d7e;
}

.btn-secondary {
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
}

.btn-secondary:hover {
  background-color: #e9ecef;
}

/* Header Account Dropdown */
.user-account {
  position: relative;
}

.account-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 15px;
  z-index: 100;
  display: none;
}

.user-account:hover .account-dropdown {
  display: block;
}

.account-info {
  display: flex;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 10px;
}

.account-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
}

.account-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.account-name p {
  font-weight: 500;
  margin: 0 0 3px;
  color: #333;
}

.account-name small {
  color: #6c757d;
  font-size: 12px;
}

.account-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.account-links li {
  margin-bottom: 5px;
}

.account-links a {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  color: #495057;
  text-decoration: none;
  border-radius: 5px;
  transition: all 0.3s;
}

.account-links a:hover {
  background-color: #f8f9fa;
  color: #00b794;
}

.account-links a.active {
  background-color: #e8f7f4;
  color: #00b794;
}

.account-links i {
  margin-right: 10px;
  width: 16px;
  text-align: center;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 40px;
  height: 40px;
  background-color: #00b794;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  z-index: 99;
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: #009d7e;
}

/* Responsive Styles */
@media (max-width: 991px) {
  .profile-container {
    flex-direction: column;
  }

  .profile-sidebar {
    width: 100%;
  }
}

@media (max-width: 767px) {
  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .profile-body {
    padding: 20px;
  }

  .radio-group {
    flex-direction: column;
    gap: 10px;
  }
}
