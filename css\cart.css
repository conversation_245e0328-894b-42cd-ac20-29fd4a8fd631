/* Cart Page Styles */

/* Breadcrumb Styles */
.breadcrumb-container {
  background-color: #f8f9fa;
  padding: 15px 0;
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.breadcrumb a {
  color: #555;
  text-decoration: none;
  transition: color 0.3s;
}

.breadcrumb a:hover {
  color: var(--primary-color);
}

.breadcrumb .separator {
  margin: 0 10px;
  color: #ccc;
}

.breadcrumb .current {
  color: var(--primary-color);
  font-weight: 500;
}

/* Cart Section */
.cart-section {
  padding: 40px 0 80px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 30px;
  color: #333;
}

.cart-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  margin-bottom: 60px;
}

/* Cart Items */
.cart-items {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.cart-header {
  display: grid;
  grid-template-columns: 3fr 1fr 1.5fr 1.5fr 0.5fr;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  color: #555;
}

.cart-header-item {
  text-align: center;
}

.cart-header-item.product-col {
  text-align: left;
}

#cart-items-container {
  min-height: 200px;
  position: relative;
}

.cart-item {
  display: grid;
  grid-template-columns: 3fr 1fr 1.5fr 1.5fr 0.5fr;
  padding: 20px;
  border-bottom: 1px solid #eee;
  align-items: center;
}

.cart-item:last-child {
  border-bottom: none;
}

.product-col {
  display: flex;
  align-items: center;
  gap: 15px;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex-grow: 1;
}

.product-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 5px;
  color: #333;
}

.product-variant {
  font-size: 14px;
  color: #777;
  margin: 0;
}

.price-col,
.total-col {
  text-align: center;
  font-weight: 600;
  color: #333;
}

.quantity-col {
  display: flex;
  justify-content: center;
}

.quantity-selector {
  display: flex;
  align-items: center;
  max-width: 120px;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  background-color: #f1f1f1;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover {
  background-color: #e1e1e1;
}

.quantity-input {
  width: 40px;
  height: 30px;
  border: 1px solid #ddd;
  border-radius: 5px;
  text-align: center;
  font-size: 14px;
  margin: 0 5px;
}

.action-col {
  text-align: center;
}

.remove-item {
  background: none;
  border: none;
  color: #999;
  font-size: 16px;
  cursor: pointer;
  transition: color 0.3s;
}

.remove-item:hover {
  color: #ff5252;
}

/* Empty Cart Message */
.empty-cart-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%;
  padding: 20px;
}

.empty-cart-icon {
  font-size: 60px;
  color: #ddd;
  margin-bottom: 20px;
}

.empty-cart-message h3 {
  font-size: 20px;
  margin-bottom: 10px;
  color: #555;
}

.empty-cart-message p {
  color: #777;
  margin-bottom: 20px;
}

/* Cart Summary */
.cart-summary {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: 25px;
  position: sticky;
  top: 20px;
  height: fit-content;
}

.cart-summary h2 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #333;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 15px;
  color: #555;
}

.summary-item.total {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

/* Coupon Container */
.coupon-container {
  margin: 25px 0;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.coupon-container h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.coupon-input {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.coupon-input input {
  flex-grow: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
}

.coupon-input input:focus {
  border-color: var(--primary-color);
  outline: none;
}

#coupon-message {
  font-size: 13px;
  margin-top: 5px;
}

#coupon-message.success {
  color: #4caf50;
}

#coupon-message.error {
  color: #f44336;
}

/* Checkout Buttons */
.checkout-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 25px;
}

.checkout-btn,
.continue-shopping {
  width: 100%;
  padding: 12px;
  text-align: center;
  font-weight: 500;
}

/* Recommended Products */
.recommended-products {
  margin-top: 60px;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
  text-align: center;
  color: #333;
  position: relative;
  padding-bottom: 15px;
}

.section-title:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .cart-container {
    grid-template-columns: 1fr;
  }

  .cart-summary {
    position: static;
  }

  .product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .cart-header {
    display: none;
  }

  .cart-item {
    grid-template-columns: 1fr;
    gap: 15px;
    position: relative;
    padding-bottom: 30px;
  }

  .product-col {
    grid-column: 1 / -1;
  }

  .price-col,
  .quantity-col,
  .total-col {
    display: flex;
    align-items: center;
  }

  .price-col::before,
  .quantity-col::before,
  .total-col::before {
    content: attr(data-label);
    font-weight: 600;
    width: 100px;
    color: #777;
  }

  .action-col {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 576px) {
  .page-title {
    font-size: 24px;
  }

  .product-grid {
    grid-template-columns: 1fr;
  }

  .product-image {
    width: 60px;
    height: 60px;
  }

  .product-title {
    font-size: 14px;
  }

  .product-variant {
    font-size: 12px;
  }

  .coupon-input {
    flex-direction: column;
  }

  .coupon-input input,
  .coupon-input button {
    width: 100%;
  }
}
