// Product Detail Page JavaScript

document.addEventListener("DOMContentLoaded", function () {
  // Initialize variables
  const mainImage = document.getElementById("main-product-image");
  const thumbnails = document.querySelectorAll(".thumbnail");
  const colorOptions = document.querySelectorAll(".color-option");
  const quantityInput = document.querySelector(".quantity-input");
  const minusBtn = document.querySelector(".quantity-btn.minus");
  const plusBtn = document.querySelector(".quantity-btn.plus");
  const addToCartBtn = document.querySelector(".add-to-cart-btn");
  const buyNowBtn = document.querySelector(".buy-now-btn");
  const wishlistBtn = document.querySelector(".wishlist-btn");
  const tabBtns = document.querySelectorAll(".tab-btn");
  const tabContents = document.querySelectorAll(".tab-content");
  const ratingStars = document.querySelectorAll(".rating-select i");

  // Product Image Gallery
  thumbnails.forEach((thumbnail) => {
    thumbnail.addEventListener("click", function () {
      // Update main image
      const imageUrl = this.getAttribute("data-image");
      mainImage.src = imageUrl;

      // Update active thumbnail
      thumbnails.forEach((item) => item.classList.remove("active"));
      this.classList.add("active");
    });
  });

  // Color Selection
  colorOptions.forEach((option) => {
    option.addEventListener("click", function () {
      // Update active color
      colorOptions.forEach((item) => item.classList.remove("active"));
      this.classList.add("active");

      // Show selected color notification
      const colorName = this.getAttribute("data-color");
      showNotification(`Màu ${colorName} đã được chọn`);
    });
  });

  // Quantity Selector
  minusBtn.addEventListener("click", function () {
    let quantity = parseInt(quantityInput.value);
    if (quantity > 1) {
      quantityInput.value = quantity - 1;
    }
  });

  plusBtn.addEventListener("click", function () {
    let quantity = parseInt(quantityInput.value);
    const max = parseInt(quantityInput.getAttribute("max"));
    if (quantity < max) {
      quantityInput.value = quantity + 1;
    } else {
      showNotification("Đã đạt số lượng tối đa");
    }
  });

  // Validate quantity input
  quantityInput.addEventListener("change", function () {
    let quantity = parseInt(this.value);
    const min = parseInt(this.getAttribute("min"));
    const max = parseInt(this.getAttribute("max"));

    if (isNaN(quantity) || quantity < min) {
      this.value = min;
    } else if (quantity > max) {
      this.value = max;
      showNotification("Đã đạt số lượng tối đa");
    }
  });

  // Add to Cart
  addToCartBtn.addEventListener("click", function () {
    const productTitle = document.querySelector(".product-title").textContent;
    const quantity = parseInt(quantityInput.value);
    const color = document
      .querySelector(".color-option.active")
      .getAttribute("data-color");

    // Add to cart logic
    addToCart({
      title: productTitle,
      quantity: quantity,
      color: color,
      price: getProductPrice(),
    });

    showNotification(`Đã thêm ${quantity} sản phẩm vào giỏ hàng`);
    updateCartCount();
  });

  // Buy Now
  buyNowBtn.addEventListener("click", function () {
    const productTitle = document.querySelector(".product-title").textContent;
    const quantity = parseInt(quantityInput.value);
    const color = document
      .querySelector(".color-option.active")
      .getAttribute("data-color");

    // Add to cart and redirect to checkout
    addToCart({
      title: productTitle,
      quantity: quantity,
      color: color,
      price: getProductPrice(),
    });

    updateCartCount();
    // Redirect to checkout page
    window.location.href = "checkout.html";
  });

  // Wishlist
  wishlistBtn.addEventListener("click", function () {
    this.classList.toggle("active");

    if (this.classList.contains("active")) {
      this.innerHTML = '<i class="fas fa-heart"></i>';
      showNotification("Đã thêm vào danh sách yêu thích");
    } else {
      this.innerHTML = '<i class="far fa-heart"></i>';
      showNotification("Đã xóa khỏi danh sách yêu thích");
    }
  });

  // Product Tabs
  tabBtns.forEach((btn) => {
    btn.addEventListener("click", function () {
      const tabId = this.getAttribute("data-tab");

      // Update active tab button
      tabBtns.forEach((item) => item.classList.remove("active"));
      this.classList.add("active");

      // Update active tab content
      tabContents.forEach((content) => {
        content.classList.remove("active");
        if (content.id === tabId) {
          content.classList.add("active");
        }
      });
    });
  });

  // Review Rating Selection
  ratingStars.forEach((star) => {
    star.addEventListener("mouseover", function () {
      const rating = parseInt(this.getAttribute("data-rating"));
      updateStars(rating);
    });

    star.addEventListener("mouseout", function () {
      const selectedRating = getSelectedRating();
      updateStars(selectedRating);
    });

    star.addEventListener("click", function () {
      const rating = parseInt(this.getAttribute("data-rating"));
      setSelectedRating(rating);
    });
  });

  // Review Form Submission
  const reviewForm = document.querySelector(".review-form");
  if (reviewForm) {
    reviewForm.addEventListener("submit", function (e) {
      e.preventDefault();

      const rating = getSelectedRating();
      const title = document.getElementById("review-title").value;
      const text = document.getElementById("review-text").value;

      if (rating === 0) {
        showNotification("Vui lòng chọn đánh giá sao", "error");
        return;
      }

      if (title.trim() === "") {
        showNotification("Vui lòng nhập tiêu đề đánh giá", "error");
        return;
      }

      if (text.trim() === "") {
        showNotification("Vui lòng nhập nội dung đánh giá", "error");
        return;
      }

      // Submit review logic would go here
      // For now, just show a success message
      showNotification("Cảm ơn bạn đã gửi đánh giá!", "success");

      // Reset form
      this.reset();
      setSelectedRating(0);
    });
  }

  // Handle file upload preview
  const fileInput = document.getElementById("review-photos");
  if (fileInput) {
    fileInput.addEventListener("change", function () {
      // File upload preview logic would go here
      if (this.files.length > 0) {
        const fileCount = this.files.length;
        const fileLabel = document.querySelector(".file-upload-btn");
        fileLabel.innerHTML = `<i class="fas fa-camera"></i> ${fileCount} hình ảnh đã chọn`;
      }
    });
  }

  // Get product ID from URL
  function getProductIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('id');
  }

  const productData = {
    1: {
      title: "Model X Pro",
      category: "Sedan điện",
      price: "************* ₫",
      description: "Model X Pro là mẫu sedan điện cao cấp với thiết kế hiện đại...",
      images: {
        main: "images/car1.jpg",
        interior: "images/car1-interior.jpg",
        rear: "images/car1-rear.jpg",
        side: "images/car1-side.jpg"
      }
    },
    2: {
      title: "Model Y Elite",
      category: "SUV điện",
      price: "1.500.000.000 ₫",
      description: "Model Y Elite là mẫu SUV điện cao cấp với không gian rộng rãi...",
      images: {
        main: "images/car2.jpg",
        interior: "images/car2-interior.jpg",
        rear: "images/car2-rear.jpg",
        side: "images/car2-side.jpg"
      }
    },
    3: {
      title: "Model Z Sport",
      category: "Sport điện",
      price: "************* ₫",
      description: "Model Z Sport là mẫu xe thể thao điện với hiệu suất cao...",
      images: {
        main: "images/car3.jpg",
        interior: "images/car3-interior.jpg",
        rear: "images/car3-rear.jpg",
        side: "images/car3-side.jpg"
      }
    }
  };

  function loadProductData() {
    const productId = getProductIdFromUrl();
    if (!productId || !productData[productId]) {
      // Nếu không có ID hoặc ID không hợp lệ, chuyển về trang sản phẩm
      window.location.href = "products.html";
      return;
    }

    const product = productData[productId];
    
    // Cập nhật tiêu đề trang
    document.title = `${product.title} - ElectroDrive`;
    
    // Cập nhật breadcrumb
    document.querySelector('.breadcrumb .current').textContent = product.title;
    
    // Cập nhật thông tin sản phẩm
    document.querySelector('.product-title').textContent = product.title;
    document.querySelector('.product-category').textContent = product.category;
    document.querySelector('.product-price .price').textContent = product.price;
    document.querySelector('.product-description p').textContent = product.description;
    
    // Cập nhật hình ảnh
    const mainImage = document.getElementById('main-product-image');
    mainImage.src = product.images.main;
    mainImage.alt = product.title;
    
    // Cập nhật thumbnails
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails[0].setAttribute('data-image', product.images.main);
    thumbnails[0].querySelector('img').src = product.images.main;
    
    thumbnails[1].setAttribute('data-image', product.images.interior);
    thumbnails[1].querySelector('img').src = product.images.interior;
    
    thumbnails[2].setAttribute('data-image', product.images.rear);
    thumbnails[2].querySelector('img').src = product.images.rear;
    
    thumbnails[3].setAttribute('data-image', product.images.side);
    thumbnails[3].querySelector('img').src = product.images.side;
  }

  // Initialize product page
  loadProductData();
  updateCartCount();

  // Helper Functions
  function showNotification(message, type = "info") {
    // Check if notification container exists, if not create it
    let notificationContainer = document.querySelector(
      ".notification-container"
    );
    if (!notificationContainer) {
      notificationContainer = document.createElement("div");
      notificationContainer.className = "notification-container";
      document.body.appendChild(notificationContainer);
    }

    // Create notification element
    const notification = document.createElement("div");
    notification.className = `notification ${type}`;
    notification.innerHTML = message;

    // Add to container
    notificationContainer.appendChild(notification);

    // Remove after delay
    setTimeout(() => {
      notification.classList.add("hide");
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 3000);
  }

  function getProductPrice() {
    const priceElement = document.querySelector(".price");
    const priceText = priceElement.textContent.trim();
    // Remove currency symbol and format, then parse as number
    return parseFloat(priceText.replace(/[^0-9]/g, ""));
  }

  function addToCart(product) {
    // Get existing cart from localStorage
    let cart = JSON.parse(localStorage.getItem("cart")) || [];

    // Check if product already exists in cart
    const existingProductIndex = cart.findIndex(
      (item) => item.title === product.title && item.color === product.color
    );

    if (existingProductIndex !== -1) {
      // Update quantity if product exists
      cart[existingProductIndex].quantity += product.quantity;
    } else {
      // Add new product to cart
      cart.push(product);
    }

    // Save updated cart to localStorage
    localStorage.setItem("cart", JSON.stringify(cart));
  }

  function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem("cart")) || [];
    const cartCount = cart.reduce((total, item) => total + item.quantity, 0);

    // Update cart count in header
    const cartCountElement = document.querySelector(".cart-count");
    if (cartCountElement) {
      cartCountElement.textContent = cartCount;
    }
  }

  function updateStars(rating) {
    ratingStars.forEach((star, index) => {
      if (index < rating) {
        star.className = "fas fa-star active";
      } else {
        star.className = "far fa-star";
      }
    });
  }

  function getSelectedRating() {
    const activeStars = document.querySelectorAll(".rating-select i.active");
    return activeStars.length;
  }

  function setSelectedRating(rating) {
    updateStars(rating);
  }
});

// Add CSS for notifications
const notificationStyles = document.createElement("style");
notificationStyles.textContent = `
    .notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
    }
    
    .notification {
        background-color: #fff;
        color: #333;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 5px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        animation: slideIn 0.3s ease-out forwards;
        max-width: 300px;
    }
    
    .notification.info {
        border-left: 4px solid var(--primary-color);
    }
    
    .notification.success {
        border-left: 4px solid #4CAF50;
    }
    
    .notification.error {
        border-left: 4px solid #F44336;
    }
    
    .notification.hide {
        animation: slideOut 0.3s ease-out forwards;
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;

document.head.appendChild(notificationStyles);
