// Main JavaScript for ElectroDrive Website

document.addEventListener("DOMContentLoaded", function () {
  // Mobile Menu Toggle
  const mobileMenuBtn = document.querySelector(".mobile-menu-btn");
  const menu = document.querySelector(".menu");

  if (mobileMenuBtn) {
    mobileMenuBtn.addEventListener("click", function () {
      this.classList.toggle("active");
      menu.classList.toggle("active");
    });
  }

  // Testimonial Slider
  const testimonialSlides = document.querySelectorAll(".testimonial-slide");
  const dots = document.querySelectorAll(".dot");
  const prevBtn = document.querySelector(".prev-btn");
  const nextBtn = document.querySelector(".next-btn");
  let currentSlide = 0;

  function showSlide(n) {
    // Hide all slides
    testimonialSlides.forEach((slide) => {
      slide.classList.remove("active");
    });

    // Remove active class from all dots
    dots.forEach((dot) => {
      dot.classList.remove("active");
    });

    // Show the current slide and activate the corresponding dot
    testimonialSlides[n].classList.add("active");
    dots[n].classList.add("active");
    currentSlide = n;
  }

  // Next slide
  function nextSlide() {
    currentSlide = (currentSlide + 1) % testimonialSlides.length;
    showSlide(currentSlide);
  }

  // Previous slide
  function prevSlide() {
    currentSlide =
      (currentSlide - 1 + testimonialSlides.length) % testimonialSlides.length;
    showSlide(currentSlide);
  }

  // Event listeners for testimonial controls
  if (nextBtn) {
    nextBtn.addEventListener("click", nextSlide);
  }

  if (prevBtn) {
    prevBtn.addEventListener("click", prevSlide);
  }

  // Add click event to dots
  dots.forEach((dot, index) => {
    dot.addEventListener("click", () => {
      showSlide(index);
    });
  });

  // Auto slide every 5 seconds
  setInterval(nextSlide, 5000);

  // Back to Top Button
  const backToTopBtn = document.querySelector(".back-to-top");

  window.addEventListener("scroll", function () {
    if (window.pageYOffset > 300) {
      backToTopBtn.classList.add("show");
    } else {
      backToTopBtn.classList.remove("show");
    }
  });

  if (backToTopBtn) {
    backToTopBtn.addEventListener("click", function (e) {
      e.preventDefault();
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    });
  }

  // Form Validation
  const contactForm = document.getElementById("contactForm");

  if (contactForm) {
    contactForm.addEventListener("submit", function (e) {
      e.preventDefault();

      // Simple validation
      let valid = true;
      const name = document.getElementById("name");
      const email = document.getElementById("email");
      const message = document.getElementById("message");

      if (!name.value.trim()) {
        valid = false;
        showError(name, "Vui lòng nhập họ tên");
      } else {
        removeError(name);
      }

      if (!email.value.trim()) {
        valid = false;
        showError(email, "Vui lòng nhập email");
      } else if (!isValidEmail(email.value)) {
        valid = false;
        showError(email, "Email không hợp lệ");
      } else {
        removeError(email);
      }

      if (!message.value.trim()) {
        valid = false;
        showError(message, "Vui lòng nhập nội dung");
      } else {
        removeError(message);
      }

      if (valid) {
        // Simulate form submission
        const submitBtn = contactForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.textContent = "Đang gửi...";

        setTimeout(function () {
          contactForm.reset();
          submitBtn.disabled = false;
          submitBtn.textContent = originalText;

          // Show success message
          const successMessage = document.createElement("div");
          successMessage.className = "success-message";
          successMessage.textContent =
            "Tin nhắn của bạn đã được gửi thành công!";

          contactForm.appendChild(successMessage);

          setTimeout(function () {
            successMessage.remove();
          }, 3000);
        }, 1500);
      }
    });
  }

  // Newsletter Form
  const newsletterForm = document.querySelector(".newsletter-form");

  if (newsletterForm) {
    newsletterForm.addEventListener("submit", function (e) {
      e.preventDefault();

      const emailInput = this.querySelector('input[type="email"]');
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;

      if (!emailInput.value.trim()) {
        showError(emailInput, "Vui lòng nhập email");
        return;
      } else if (!isValidEmail(emailInput.value)) {
        showError(emailInput, "Email không hợp lệ");
        return;
      } else {
        removeError(emailInput);
      }

      // Simulate form submission
      submitBtn.disabled = true;
      submitBtn.textContent = "Đang đăng ký...";

      setTimeout(function () {
        emailInput.value = "";
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;

        // Show success message
        const newsletterContent = document.querySelector(".newsletter-content");
        const successMessage = document.createElement("div");
        successMessage.className = "success-message";
        successMessage.textContent = "Cảm ơn bạn đã đăng ký!";

        newsletterContent.appendChild(successMessage);

        setTimeout(function () {
          successMessage.remove();
        }, 3000);
      }, 1500);
    });
  }

  // Helper Functions
  function showError(input, message) {
    const formGroup = input.parentElement;
    let errorMessage = formGroup.querySelector(".error-message");

    if (!errorMessage) {
      errorMessage = document.createElement("div");
      errorMessage.className = "error-message";
      formGroup.appendChild(errorMessage);
    }

    errorMessage.textContent = message;
    formGroup.classList.add("error");
  }

  function removeError(input) {
    const formGroup = input.parentElement;
    const errorMessage = formGroup.querySelector(".error-message");

    if (errorMessage) {
      errorMessage.remove();
    }

    formGroup.classList.remove("error");
  }

  function isValidEmail(email) {
    const re =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  }

  // Add to Cart Functionality (Simple Demo)
  const addToCartButtons = document.querySelectorAll(".btn-secondary");
  const cartCount = document.querySelector(".cart-count");

  if (addToCartButtons.length > 0 && cartCount) {
    let count = parseInt(cartCount.textContent) || 0;

    addToCartButtons.forEach((button) => {
      button.addEventListener("click", function (e) {
        // Only increment if the button text is "Thêm vào giỏ"
        if (this.textContent.includes("Thêm vào giỏ")) {
          e.preventDefault();
          count++;
          cartCount.textContent = count;

          // Show notification
          const notification = document.createElement("div");
          notification.className = "cart-notification";
          notification.textContent = "Đã thêm sản phẩm vào giỏ hàng!";
          document.body.appendChild(notification);

          setTimeout(() => {
            notification.classList.add("show");
          }, 10);

          setTimeout(() => {
            notification.classList.remove("show");
            setTimeout(() => {
              notification.remove();
            }, 300);
          }, 2000);
        }
      });
    });
  }

  // Search Functionality
  const searchIcon = document.querySelector(".search-icon");

  if (searchIcon) {
    searchIcon.addEventListener("click", function (e) {
      e.preventDefault();

      // Create search overlay
      const searchOverlay = document.createElement("div");
      searchOverlay.className = "search-overlay";

      const searchContainer = document.createElement("div");
      searchContainer.className = "search-container";

      const searchForm = document.createElement("form");
      searchForm.className = "search-form";

      const searchInput = document.createElement("input");
      searchInput.type = "text";
      searchInput.placeholder = "Tìm kiếm sản phẩm...";
      searchInput.className = "search-input";

      const closeBtn = document.createElement("button");
      closeBtn.type = "button";
      closeBtn.className = "search-close";
      closeBtn.innerHTML = '<i class="fas fa-times"></i>';

      const searchBtn = document.createElement("button");
      searchBtn.type = "submit";
      searchBtn.className = "search-button";
      searchBtn.innerHTML = '<i class="fas fa-search"></i>';

      searchForm.appendChild(searchInput);
      searchForm.appendChild(searchBtn);
      searchContainer.appendChild(searchForm);
      searchContainer.appendChild(closeBtn);
      searchOverlay.appendChild(searchContainer);
      document.body.appendChild(searchOverlay);

      // Focus the input
      setTimeout(() => {
        searchInput.focus();
      }, 100);

      // Close search overlay
      closeBtn.addEventListener("click", function () {
        document.body.removeChild(searchOverlay);
      });

      // Handle search form submission
      searchForm.addEventListener("submit", function (e) {
        e.preventDefault();

        if (searchInput.value.trim()) {
          // Redirect to search results page (demo)
          window.location.href =
            "products.html?search=" +
            encodeURIComponent(searchInput.value.trim());
        }
      });

      // Close on escape key
      document.addEventListener("keydown", function (e) {
        if (e.key === "Escape") {
          if (document.body.contains(searchOverlay)) {
            document.body.removeChild(searchOverlay);
          }
        }
      });
    });
  }

  // Add CSS for dynamic elements
  const style = document.createElement("style");
  style.textContent = `
        .error-message {
            color: #f44336;
            font-size: 0.85rem;
            margin-top: 5px;
        }
        
        .form-group.error input,
        .form-group.error textarea,
        .form-group.error select {
            border-color: #f44336;
        }
        
        .success-message {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-top: 15px;
            text-align: center;
        }
        
        .cart-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .cart-notification.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .search-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .search-container {
            position: relative;
            width: 80%;
            max-width: 600px;
        }
        
        .search-form {
            display: flex;
        }
        
        .search-input {
            flex: 1;
            padding: 15px 20px;
            font-size: 1.2rem;
            border: none;
            border-radius: 5px 0 0 5px;
            outline: none;
        }
        
        .search-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 0 20px;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
        }
        
        .search-close {
            position: absolute;
            top: -40px;
            right: 0;
            background: transparent;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }
    `;
  document.head.appendChild(style);
});
