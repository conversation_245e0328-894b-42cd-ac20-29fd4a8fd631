// JavaScript for Products Page

document.addEventListener("DOMContentLoaded", function () {
  // Price Range Slider
  const minPriceRange = document.querySelector(".min-price-range");
  const maxPriceRange = document.querySelector(".max-price-range");
  const minPriceInput = document.getElementById("min-price");
  const maxPriceInput = document.getElementById("max-price");
  const sliderTrack = document.querySelector(".slider-track");

  // Convert price to display format (millions)
  function formatPrice(price) {
    return (
      price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + ".000.000 ₫"
    );
  }

  // Convert display price to number
  function parsePrice(priceStr) {
    return parseInt(priceStr.replace(/[^\d]/g, "")) / 1000000;
  }

  // Update slider track
  function updateSliderTrack() {
    const minVal = parseInt(minPriceRange.value);
    const maxVal = parseInt(maxPriceRange.value);
    const minPercent = (minVal / parseInt(minPriceRange.max)) * 100;
    const maxPercent = (maxVal / parseInt(maxPriceRange.max)) * 100;

    sliderTrack.style.left = minPercent + "%";
    sliderTrack.style.width = maxPercent - minPercent + "%";

    minPriceInput.value = minVal;
    maxPriceInput.value = maxVal;
  }

  // Initialize slider track
  if (minPriceRange && maxPriceRange) {
    updateSliderTrack();

    // Event listeners for range inputs
    minPriceRange.addEventListener("input", function () {
      if (parseInt(minPriceRange.value) > parseInt(maxPriceRange.value) - 100) {
        minPriceRange.value = parseInt(maxPriceRange.value) - 100;
      }
      updateSliderTrack();
    });

    maxPriceRange.addEventListener("input", function () {
      if (parseInt(maxPriceRange.value) < parseInt(minPriceRange.value) + 100) {
        maxPriceRange.value = parseInt(minPriceRange.value) + 100;
      }
      updateSliderTrack();
    });

    // Event listeners for text inputs
    minPriceInput.addEventListener("change", function () {
      let value = parseInt(this.value) || 0;
      if (value < 0) value = 0;
      if (value > parseInt(maxPriceRange.value) - 100) {
        value = parseInt(maxPriceRange.value) - 100;
      }
      minPriceRange.value = value;
      updateSliderTrack();
    });

    maxPriceInput.addEventListener("change", function () {
      let value = parseInt(this.value) || 3000;
      if (value > 3000) value = 3000;
      if (value < parseInt(minPriceRange.value) + 100) {
        value = parseInt(minPriceRange.value) + 100;
      }
      maxPriceRange.value = value;
      updateSliderTrack();
    });
  }

  // Filter Apply Button
  const filterApplyBtn = document.querySelector(".filter-apply-btn");
  if (filterApplyBtn) {
    filterApplyBtn.addEventListener("click", function () {
      // Get selected categories
      const selectedCategories = [];
      document
        .querySelectorAll(".category-filter input:checked")
        .forEach((checkbox) => {
          if (checkbox.nextElementSibling.textContent !== "Tất cả") {
            selectedCategories.push(checkbox.nextElementSibling.textContent);
          }
        });

      // Get price range
      const minPrice = parseInt(minPriceInput.value) || 0;
      const maxPrice = parseInt(maxPriceInput.value) || 3000;

      // Get selected ranges
      const selectedRanges = [];
      document
        .querySelectorAll(".range-filter input:checked")
        .forEach((checkbox) => {
          selectedRanges.push(checkbox.nextElementSibling.textContent);
        });

      // Get selected charging times
      const selectedChargingTimes = [];
      document
        .querySelectorAll(".charging-filter input:checked")
        .forEach((checkbox) => {
          selectedChargingTimes.push(checkbox.nextElementSibling.textContent);
        });

      // Apply filters (demo - just log to console)
      console.log("Applying filters:");
      console.log("Categories:", selectedCategories);
      console.log("Price Range:", minPrice + " - " + maxPrice + " million VND");
      console.log("Driving Ranges:", selectedRanges);
      console.log("Charging Times:", selectedChargingTimes);

      // In a real application, you would filter the products based on these criteria
      // For demo purposes, we'll just show a notification
      showFilterNotification();
    });
  }

  // Filter Reset Button
  const filterResetBtn = document.querySelector(".filter-reset-btn");
  if (filterResetBtn) {
    filterResetBtn.addEventListener("click", function () {
      // Reset checkboxes
      document
        .querySelectorAll('input[type="checkbox"]')
        .forEach((checkbox) => {
          checkbox.checked =
            checkbox.parentElement.textContent.trim() === "Tất cả";
        });

      // Reset price range
      minPriceRange.value = 0;
      maxPriceRange.value = 3000;
      updateSliderTrack();

      console.log("Filters reset");
      showFilterNotification("Đã đặt lại bộ lọc");
    });
  }

  // Sort Select
  const sortSelect = document.getElementById("sort-select");
  if (sortSelect) {
    sortSelect.addEventListener("change", function () {
      const sortValue = this.value;
      const productCards = Array.from(
        document.querySelectorAll(".product-card")
      );
      const productGrid = document.querySelector(".product-grid");

      // Sort products based on selected option
      productCards.sort((a, b) => {
        const priceA = parsePrice(a.querySelector(".price").textContent);
        const priceB = parsePrice(b.querySelector(".price").textContent);
        const nameA = a.querySelector("h3").textContent;
        const nameB = b.querySelector("h3").textContent;

        switch (sortValue) {
          case "price-low":
            return priceA - priceB;
          case "price-high":
            return priceB - priceA;
          case "name-asc":
            return nameA.localeCompare(nameB);
          case "name-desc":
            return nameB.localeCompare(nameA);
          default:
            return 0; // Default sorting (as is)
        }
      });

      // Clear and re-append sorted products
      productGrid.innerHTML = "";
      productCards.forEach((card) => {
        productGrid.appendChild(card);
      });

      console.log("Products sorted by:", sortValue);
      showFilterNotification("Đã sắp xếp sản phẩm");
    });
  }

  // Show filter notification
  function showFilterNotification(message = "Đã áp dụng bộ lọc") {
    const notification = document.createElement("div");
    notification.className = "filter-notification";
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.classList.add("show");
    }, 10);

    setTimeout(() => {
      notification.classList.remove("show");
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 2000);
  }

  // Add CSS for filter notification
  const style = document.createElement("style");
  style.textContent = `
        .filter-notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .filter-notification.show {
            opacity: 1;
            transform: translateY(0);
        }
    `;
  document.head.appendChild(style);

  // Product Actions
  const actionBtns = document.querySelectorAll(".action-btn");
  actionBtns.forEach((btn) => {
    btn.addEventListener("click", function (e) {
      const icon = this.querySelector("i");

      // Handle different actions based on icon
      if (icon.classList.contains("fa-heart")) {
        e.preventDefault();
        icon.classList.toggle("fas");
        icon.classList.toggle("far");
        const message = icon.classList.contains("fas")
          ? "Đã thêm vào danh sách yêu thích"
          : "Đã xóa khỏi danh sách yêu thích";
        showFilterNotification(message);
      } else if (icon.classList.contains("fa-shopping-cart")) {
        e.preventDefault();
        const cartCount = document.querySelector(".cart-count");
        let count = parseInt(cartCount.textContent) || 0;
        count++;
        cartCount.textContent = count;
        showFilterNotification("Đã thêm vào giỏ hàng");
      }
    });
  });

  // Initialize heart icons as regular (not solid)
  document.querySelectorAll(".action-btn i.fa-heart").forEach((icon) => {
    icon.classList.remove("fas");
    icon.classList.add("far");
  });

  // Handle URL parameters for search
  function handleURLParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const searchQuery = urlParams.get("search");

    if (searchQuery) {
      // Display search query
      const productsHeader = document.querySelector(".products-header");
      const searchInfo = document.createElement("div");
      searchInfo.className = "search-info";
      searchInfo.innerHTML = `<p>Kết quả tìm kiếm cho: <strong>${searchQuery}</strong></p>`;

      productsHeader.prepend(searchInfo);

      // Add style for search info
      const searchStyle = document.createElement("style");
      searchStyle.textContent = `
                .search-info {
                    background-color: #f5f5f5;
                    padding: 10px 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                    width: 100%;
                }
                
                .search-info p {
                    margin: 0;
                }
                
                .search-info strong {
                    color: #4CAF50;
                }
            `;
      document.head.appendChild(searchStyle);
    }
  }

  handleURLParams();
});
