/**
 * Profile Page JavaScript
 * Handles user profile functionality
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize profile functionality
  initProfile();
});

/**
 * Initialize profile page functionality
 */
function initProfile() {
  // Check if user is logged in
  checkUserLogin();

  // Initialize form with user data
  loadUserData();

  // Initialize password toggle
  initPasswordToggle();

  // Initialize password strength meter
  initPasswordStrength();

  // Initialize avatar upload
  initAvatarUpload();

  // Initialize form submission
  initFormSubmission();

  // Initialize logout functionality
  initLogout();

  // Initialize back to top button
  initBackToTop();
}

/**
 * Check if user is logged in, redirect to login page if not
 */
function checkUserLogin() {
  const user = getUserData();

  if (!user || !user.isLoggedIn) {
    // Redirect to login page
    window.location.href = "login.html";
  } else {
    // Update header user info
    updateUserInfo(user);
  }
}

/**
 * Get user data from localStorage or sessionStorage
 */
function getUserData() {
  let user = JSON.parse(localStorage.getItem("user"));

  if (!user) {
    user = JSON.parse(sessionStorage.getItem("user"));
  }

  return user;
}

/**
 * Update user info in header and sidebar
 */
function updateUserInfo(user) {
  // Update header dropdown
  const userNameElement = document.getElementById("user-name");
  const userEmailElement = document.getElementById("user-email");
  const userAvatarElement = document.getElementById("user-avatar");

  if (userNameElement)
    userNameElement.textContent = user.name || "Người dùng ElectroDrive";
  if (userEmailElement) userEmailElement.textContent = user.email || "";
  if (userAvatarElement && user.avatar) userAvatarElement.src = user.avatar;

  // Update sidebar
  const sidebarNameElement = document.getElementById("sidebar-name");
  const sidebarEmailElement = document.getElementById("sidebar-email");
  const sidebarAvatarElement = document.getElementById("sidebar-avatar");

  if (sidebarNameElement)
    sidebarNameElement.textContent = user.name || "Người dùng ElectroDrive";
  if (sidebarEmailElement) sidebarEmailElement.textContent = user.email || "";
  if (sidebarAvatarElement && user.avatar)
    sidebarAvatarElement.src = user.avatar;
}

/**
 * Load user data into form
 */
function loadUserData() {
  const user = getUserData();

  if (!user) return;

  // Split name into first and last name (simple split by space)
  let firstName = "";
  let lastName = "";

  if (user.name) {
    const nameParts = user.name.split(" ");
    if (nameParts.length > 1) {
      lastName = nameParts.pop();
      firstName = nameParts.join(" ");
    } else {
      lastName = user.name;
    }
  }

  // Set form values
  const firstNameInput = document.getElementById("first-name");
  const lastNameInput = document.getElementById("last-name");
  const emailInput = document.getElementById("email");
  const phoneInput = document.getElementById("phone");
  const birthdayInput = document.getElementById("birthday");
  const genderInputs = document.querySelectorAll('input[name="gender"]');
  const newsletterInput = document.getElementById("newsletter-subscribe");

  if (firstNameInput) firstNameInput.value = firstName;
  if (lastNameInput) lastNameInput.value = lastName;
  if (emailInput) emailInput.value = user.email || "";
  if (phoneInput) phoneInput.value = user.phone || "";
  if (birthdayInput) birthdayInput.value = user.birthday || "";

  // Set gender
  if (user.gender) {
    genderInputs.forEach((input) => {
      if (input.value === user.gender) {
        input.checked = true;
      }
    });
  }

  // Set newsletter subscription
  if (newsletterInput)
    newsletterInput.checked = user.newsletterSubscribe || false;
}

/**
 * Initialize password toggle functionality
 */
function initPasswordToggle() {
  const toggleButtons = document.querySelectorAll(".toggle-password");

  toggleButtons.forEach((button) => {
    button.addEventListener("click", function () {
      const input = this.previousElementSibling;
      const icon = this.querySelector("i");

      if (input.type === "password") {
        input.type = "text";
        icon.classList.remove("fa-eye");
        icon.classList.add("fa-eye-slash");
      } else {
        input.type = "password";
        icon.classList.remove("fa-eye-slash");
        icon.classList.add("fa-eye");
      }
    });
  });
}

/**
 * Initialize password strength meter
 */
function initPasswordStrength() {
  const passwordInput = document.getElementById("new-password");

  if (passwordInput) {
    passwordInput.addEventListener("input", updatePasswordStrength);
  }
}

/**
 * Update password strength meter
 */
function updatePasswordStrength() {
  const password = this.value;
  const meter = document.querySelector(".strength-meter-fill");
  const text = document.querySelector(".strength-text span");

  if (!meter || !text) return;

  if (!password) {
    meter.setAttribute("data-strength", "0");
    text.textContent = "Yếu";
    return;
  }

  // Calculate password strength
  let strength = 0;

  // Length check
  if (password.length >= 8) strength += 1;
  if (password.length >= 12) strength += 1;

  // Complexity checks
  if (/[A-Z]/.test(password)) strength += 1;
  if (/[0-9]/.test(password)) strength += 1;
  if (/[^A-Za-z0-9]/.test(password)) strength += 1;

  // Cap at 4
  strength = Math.min(strength, 4);

  // Update meter
  meter.setAttribute("data-strength", strength);

  // Update text
  const strengthTexts = ["Yếu", "Yếu", "Trung bình", "Khá", "Mạnh"];
  text.textContent = strengthTexts[strength];
}

/**
 * Initialize avatar upload functionality
 */
function initAvatarUpload() {
  const changeAvatarBtn = document.getElementById("change-avatar-btn");
  const avatarUploadInput = document.getElementById("avatar-upload");

  if (changeAvatarBtn && avatarUploadInput) {
    changeAvatarBtn.addEventListener("click", function () {
      avatarUploadInput.click();
    });

    avatarUploadInput.addEventListener("change", function () {
      if (this.files && this.files[0]) {
        const file = this.files[0];

        // Check if file is an image
        if (!file.type.match("image.*")) {
          showNotification("Vui lòng chọn file hình ảnh", "error");
          return;
        }

        // Check file size (max 2MB)
        if (file.size > 2 * 1024 * 1024) {
          showNotification("Kích thước file không được vượt quá 2MB", "error");
          return;
        }

        const reader = new FileReader();

        reader.onload = function (e) {
          // Update avatar preview
          const sidebarAvatar = document.getElementById("sidebar-avatar");
          const headerAvatar = document.getElementById("user-avatar");

          if (sidebarAvatar) sidebarAvatar.src = e.target.result;
          if (headerAvatar) headerAvatar.src = e.target.result;

          // Save avatar to user data
          const user = getUserData();
          if (user) {
            user.avatar = e.target.result;
            saveUserData(user);
            showNotification("Ảnh đại diện đã được cập nhật", "success");
          }
        };

        reader.readAsDataURL(file);
      }
    });
  }
}

/**
 * Initialize form submission
 */
function initFormSubmission() {
  const profileForm = document.getElementById("profile-form");

  if (profileForm) {
    profileForm.addEventListener("submit", function (e) {
      e.preventDefault();

      // Get form data
      const firstName = document.getElementById("first-name").value;
      const lastName = document.getElementById("last-name").value;
      const email = document.getElementById("email").value;
      const phone = document.getElementById("phone").value;
      const birthday = document.getElementById("birthday").value;

      // Get gender
      let gender = "";
      const genderInputs = document.querySelectorAll('input[name="gender"]');
      genderInputs.forEach((input) => {
        if (input.checked) {
          gender = input.value;
        }
      });

      // Get newsletter subscription
      const newsletterSubscribe = document.getElementById(
        "newsletter-subscribe"
      ).checked;

      // Validate form
      if (!firstName || !lastName) {
        showNotification("Vui lòng nhập họ và tên của bạn", "error");
        return;
      }

      if (!validateEmail(email)) {
        showNotification("Vui lòng nhập địa chỉ email hợp lệ", "error");
        return;
      }

      if (phone && !validatePhone(phone)) {
        showNotification("Số điện thoại không hợp lệ", "error");
        return;
      }

      // Check if password is being changed
      const currentPassword = document.getElementById("current-password").value;
      const newPassword = document.getElementById("new-password").value;
      const confirmPassword = document.getElementById("confirm-password").value;

      if (currentPassword || newPassword || confirmPassword) {
        // Validate password change
        if (!currentPassword) {
          showNotification("Vui lòng nhập mật khẩu hiện tại", "error");
          return;
        }

        if (!newPassword) {
          showNotification("Vui lòng nhập mật khẩu mới", "error");
          return;
        }

        if (newPassword.length < 8) {
          showNotification("Mật khẩu mới phải có ít nhất 8 ký tự", "error");
          return;
        }

        if (newPassword !== confirmPassword) {
          showNotification("Mật khẩu xác nhận không khớp", "error");
          return;
        }

        // In a real app, we would verify the current password with the server
        // For this demo, we'll just simulate it
        if (currentPassword !== "password123") {
          // This is just a placeholder
          showNotification("Mật khẩu hiện tại không chính xác", "error");
          return;
        }
      }

      // Get current user data
      const user = getUserData();

      if (!user) return;

      // Update user data
      user.name = `${firstName} ${lastName}`;
      user.email = email;
      user.phone = phone;
      user.birthday = birthday;
      user.gender = gender;
      user.newsletterSubscribe = newsletterSubscribe;

      // Update password if changed
      if (newPassword) {
        // In a real app, we would hash the password
        user.password = newPassword;
      }

      // Save updated user data
      saveUserData(user);

      // Update UI
      updateUserInfo(user);

      // Show success message
      showNotification(
        "Thông tin hồ sơ đã được cập nhật thành công",
        "success"
      );

      // Reset password fields
      if (currentPassword) {
        document.getElementById("current-password").value = "";
        document.getElementById("new-password").value = "";
        document.getElementById("confirm-password").value = "";

        // Reset password strength meter
        const meter = document.querySelector(".strength-meter-fill");
        const text = document.querySelector(".strength-text span");

        if (meter) meter.setAttribute("data-strength", "0");
        if (text) text.textContent = "Yếu";
      }
    });
  }
}

/**
 * Save user data to storage
 */
function saveUserData(user) {
  if (localStorage.getItem("user")) {
    localStorage.setItem("user", JSON.stringify(user));
  } else if (sessionStorage.getItem("user")) {
    sessionStorage.setItem("user", JSON.stringify(user));
  }
}

/**
 * Initialize logout functionality
 */
function initLogout() {
  const logoutButtons = document.querySelectorAll(
    "#logout-button, #sidebar-logout"
  );

  logoutButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();

      // Clear user data
      localStorage.removeItem("user");
      sessionStorage.removeItem("user");

      // Show success message
      showNotification("Đăng xuất thành công", "success");

      // Redirect to home page after a short delay
      setTimeout(function () {
        window.location.href = "index.html";
      }, 1500);
    });
  });
}

/**
 * Initialize back to top button
 */
function initBackToTop() {
  const backToTopButton = document.querySelector(".back-to-top");

  if (backToTopButton) {
    // Show/hide button based on scroll position
    window.addEventListener("scroll", function () {
      if (window.pageYOffset > 300) {
        backToTopButton.classList.add("show");
      } else {
        backToTopButton.classList.remove("show");
      }
    });

    // Scroll to top when clicked
    backToTopButton.addEventListener("click", function (e) {
      e.preventDefault();
      window.scrollTo({ top: 0, behavior: "smooth" });
    });
  }
}

/**
 * Validate email format
 */
function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

/**
 * Validate phone number format
 */
function validatePhone(phone) {
  // Basic Vietnamese phone number validation
  const re = /^(0|\+84)(3|5|7|8|9)\d{8}$/;
  return re.test(phone);
}

/**
 * Show notification message
 */
function showNotification(message, type = "info") {
  // Check if notification container exists, if not create it
  let notificationContainer = document.querySelector(".notification-container");

  if (!notificationContainer) {
    notificationContainer = document.createElement("div");
    notificationContainer.className = "notification-container";
    document.body.appendChild(notificationContainer);

    // Add styles if not already in CSS
    const style = document.createElement("style");
    style.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
            }
            
            .notification {
                padding: 15px 20px;
                margin-bottom: 10px;
                border-radius: 5px;
                color: white;
                font-weight: 500;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
                display: flex;
                align-items: center;
                justify-content: space-between;
                min-width: 300px;
                max-width: 450px;
                opacity: 0;
                transform: translateX(50px);
                transition: opacity 0.3s, transform 0.3s;
            }
            
            .notification.show {
                opacity: 1;
                transform: translateX(0);
            }
            
            .notification.info {
                background-color: #3498db;
            }
            
            .notification.success {
                background-color: #2ecc71;
            }
            
            .notification.warning {
                background-color: #f39c12;
            }
            
            .notification.error {
                background-color: #e74c3c;
            }
            
            .notification-close {
                background: none;
                border: none;
                color: white;
                font-size: 16px;
                cursor: pointer;
                margin-left: 10px;
                opacity: 0.8;
            }
            
            .notification-close:hover {
                opacity: 1;
            }
        `;
    document.head.appendChild(style);
  }

  // Create notification element
  const notification = document.createElement("div");
  notification.className = `notification ${type}`;

  // Add icon based on type
  let icon;
  switch (type) {
    case "success":
      icon = "fa-check-circle";
      break;
    case "error":
      icon = "fa-exclamation-circle";
      break;
    case "warning":
      icon = "fa-exclamation-triangle";
      break;
    default:
      icon = "fa-info-circle";
  }

  notification.innerHTML = `
        <div>
            <i class="fas ${icon}"></i> ${message}
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

  // Add to container
  notificationContainer.appendChild(notification);

  // Show notification with animation
  setTimeout(() => notification.classList.add("show"), 10);

  // Add close button functionality
  const closeButton = notification.querySelector(".notification-close");
  closeButton.addEventListener("click", () => {
    notification.classList.remove("show");
    setTimeout(() => notification.remove(), 300);
  });

  // Auto remove after 5 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.classList.remove("show");
      setTimeout(() => notification.remove(), 300);
    }
  }, 5000);
}
