/**
 * Authentication Pages JavaScript
 * Handles login, register, and forgot password functionality
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize auth functionality based on current page
  initAuth();
});

/**
 * Initialize authentication functionality based on current page
 */
function initAuth() {
  // Determine which page we're on
  if (document.querySelector("#login-form")) {
    initLogin();
  } else if (document.querySelector("#register-form")) {
    initRegister();
  } else if (document.querySelector("#forgot-password-form")) {
    initForgotPassword();
  }

  // Initialize password toggle for all auth pages
  initPasswordToggle();
}

/**
 * Initialize login page functionality
 */
function initLogin() {
  const loginForm = document.querySelector("#login-form");

  loginForm.addEventListener("submit", function (e) {
    e.preventDefault();

    const email = document.querySelector("#email").value;
    const password = document.querySelector("#password").value;
    const rememberMe = document.querySelector("#remember-me").checked;

    // Validate form
    if (!validateEmail(email)) {
      showNotification("Vui lòng nhập địa chỉ email hợp lệ", "error");
      return;
    }

    if (!password) {
      showNotification("Vui lòng nhập mật khẩu", "error");
      return;
    }

    // Simulate login API call
    simulateApiCall(
      {
        email: email,
        password: password,
        rememberMe: rememberMe,
      },
      function (response) {
        if (response.success) {
          // Store user data in localStorage
          if (rememberMe) {
            localStorage.setItem(
              "user",
              JSON.stringify({
                email: email,
                name: "Người dùng ElectroDrive", // This would come from the API in a real app
                isLoggedIn: true,
              })
            );
          } else {
            sessionStorage.setItem(
              "user",
              JSON.stringify({
                email: email,
                name: "Người dùng ElectroDrive", // This would come from the API in a real app
                isLoggedIn: true,
              })
            );
          }

          showNotification("Đăng nhập thành công!", "success");

          // Redirect to home page after a short delay
          setTimeout(function () {
            window.location.href = "index.html";
          }, 1500);
        } else {
          showNotification("Email hoặc mật khẩu không chính xác", "error");
        }
      }
    );
  });
}

/**
 * Initialize register page functionality
 */
function initRegister() {
  const registerForm = document.querySelector("#register-form");
  const passwordInput = document.querySelector("#password");

  // Initialize password strength meter
  if (passwordInput) {
    passwordInput.addEventListener("input", updatePasswordStrength);
  }

  registerForm.addEventListener("submit", function (e) {
    e.preventDefault();

    const firstName = document.querySelector("#first-name").value;
    const lastName = document.querySelector("#last-name").value;
    const email = document.querySelector("#email").value;
    const phone = document.querySelector("#phone").value;
    const password = document.querySelector("#password").value;
    const confirmPassword = document.querySelector("#confirm-password").value;
    const termsAgreed = document.querySelector("#terms-agree").checked;
    const newsletterSubscribe = document.querySelector(
      "#newsletter-subscribe"
    ).checked;

    // Validate form
    if (!firstName || !lastName) {
      showNotification("Vui lòng nhập họ và tên của bạn", "error");
      return;
    }

    if (!validateEmail(email)) {
      showNotification("Vui lòng nhập địa chỉ email hợp lệ", "error");
      return;
    }

    if (phone && !validatePhone(phone)) {
      showNotification("Số điện thoại không hợp lệ", "error");
      return;
    }

    if (!password || password.length < 8) {
      showNotification("Mật khẩu phải có ít nhất 8 ký tự", "error");
      return;
    }

    if (password !== confirmPassword) {
      showNotification("Mật khẩu xác nhận không khớp", "error");
      return;
    }

    if (!termsAgreed) {
      showNotification(
        "Bạn phải đồng ý với Điều khoản dịch vụ để tiếp tục",
        "error"
      );
      return;
    }

    // Simulate register API call
    simulateApiCall(
      {
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: phone,
        password: password,
        newsletterSubscribe: newsletterSubscribe,
      },
      function (response) {
        if (response.success) {
          showNotification("Đăng ký tài khoản thành công!", "success");

          // Store user data in sessionStorage
          sessionStorage.setItem(
            "user",
            JSON.stringify({
              email: email,
              name: firstName + " " + lastName,
              isLoggedIn: true,
            })
          );

          // Redirect to home page after a short delay
          setTimeout(function () {
            window.location.href = "index.html";
          }, 1500);
        } else {
          showNotification(
            "Email đã được sử dụng. Vui lòng thử email khác.",
            "error"
          );
        }
      }
    );
  });
}

/**
 * Initialize forgot password page functionality
 */
function initForgotPassword() {
  const forgotPasswordForm = document.querySelector("#forgot-password-form");
  const verificationForm = document.querySelector("#verification-form");
  const resetPasswordForm = document.querySelector("#reset-password-form");
  const successMessage = document.querySelector("#success-message");
  const newPasswordInput = document.querySelector("#new-password");

  // Initialize password strength meter for reset password form
  if (newPasswordInput) {
    newPasswordInput.addEventListener("input", updatePasswordStrength);
  }

  // Step 1: Request password reset
  forgotPasswordForm.addEventListener("submit", function (e) {
    e.preventDefault();

    const email = document.querySelector("#email").value;

    if (!validateEmail(email)) {
      showNotification("Vui lòng nhập địa chỉ email hợp lệ", "error");
      return;
    }

    // Simulate API call to request password reset
    simulateApiCall({ email: email }, function (response) {
      if (response.success) {
        // Hide step 1 form and show step 2 form
        forgotPasswordForm.style.display = "none";
        verificationForm.style.display = "block";

        // Start countdown for resend code
        startResendCountdown();

        // Setup verification code inputs
        setupVerificationInputs();

        showNotification(
          "Mã xác nhận đã được gửi đến email của bạn",
          "success"
        );
      } else {
        showNotification("Email không tồn tại trong hệ thống", "error");
      }
    });
  });

  // Step 2: Verify code
  verificationForm.addEventListener("submit", function (e) {
    e.preventDefault();

    const inputs = document.querySelectorAll(".verification-input");
    let code = "";

    inputs.forEach((input) => {
      code += input.value;
    });

    if (code.length !== 6) {
      showNotification("Vui lòng nhập đầy đủ mã xác nhận", "error");
      return;
    }

    // Simulate API call to verify code
    simulateApiCall({ code: code }, function (response) {
      if (response.success) {
        // Hide step 2 form and show step 3 form
        verificationForm.style.display = "none";
        resetPasswordForm.style.display = "block";

        showNotification("Mã xác nhận hợp lệ", "success");
      } else {
        showNotification("Mã xác nhận không hợp lệ hoặc đã hết hạn", "error");
      }
    });
  });

  // Handle resend code
  const resendCodeBtn = document.querySelector("#resend-code");
  if (resendCodeBtn) {
    resendCodeBtn.addEventListener("click", function (e) {
      e.preventDefault();

      const countdownElement = document.querySelector("#countdown-timer");
      if (countdownElement.textContent === "0") {
        // Simulate API call to resend code
        simulateApiCall({}, function (response) {
          if (response.success) {
            showNotification(
              "Mã xác nhận mới đã được gửi đến email của bạn",
              "success"
            );
            startResendCountdown();
          } else {
            showNotification(
              "Không thể gửi lại mã xác nhận. Vui lòng thử lại sau.",
              "error"
            );
          }
        });
      }
    });
  }

  // Step 3: Reset password
  resetPasswordForm.addEventListener("submit", function (e) {
    e.preventDefault();

    const newPassword = document.querySelector("#new-password").value;
    const confirmNewPassword = document.querySelector(
      "#confirm-new-password"
    ).value;

    if (!newPassword || newPassword.length < 8) {
      showNotification("Mật khẩu mới phải có ít nhất 8 ký tự", "error");
      return;
    }

    if (newPassword !== confirmNewPassword) {
      showNotification("Mật khẩu xác nhận không khớp", "error");
      return;
    }

    // Simulate API call to reset password
    simulateApiCall({ newPassword: newPassword }, function (response) {
      if (response.success) {
        // Hide step 3 form and show success message
        resetPasswordForm.style.display = "none";
        successMessage.style.display = "block";
      } else {
        showNotification(
          "Không thể đặt lại mật khẩu. Vui lòng thử lại sau.",
          "error"
        );
      }
    });
  });
}

/**
 * Setup verification code inputs for auto-tabbing
 */
function setupVerificationInputs() {
  const inputs = document.querySelectorAll(".verification-input");

  inputs.forEach((input, index) => {
    // Focus first input on load
    if (index === 0) {
      setTimeout(() => input.focus(), 100);
    }

    // Auto-tab to next input
    input.addEventListener("input", function () {
      if (this.value.length === this.maxLength) {
        const nextInput = inputs[index + 1];
        if (nextInput) {
          nextInput.focus();
        }
      }
    });

    // Handle backspace
    input.addEventListener("keydown", function (e) {
      if (e.key === "Backspace" && !this.value) {
        const prevInput = inputs[index - 1];
        if (prevInput) {
          prevInput.focus();
        }
      }
    });

    // Handle paste event for the entire code
    input.addEventListener("paste", function (e) {
      e.preventDefault();
      const pasteData = e.clipboardData.getData("text").trim();

      if (/^\d+$/.test(pasteData) && pasteData.length <= inputs.length) {
        for (let i = 0; i < pasteData.length; i++) {
          if (inputs[i]) {
            inputs[i].value = pasteData[i];
          }
        }

        // Focus on the next empty input or the last one
        const lastFilledIndex = Math.min(
          pasteData.length - 1,
          inputs.length - 1
        );
        inputs[lastFilledIndex].focus();
      }
    });
  });
}

/**
 * Start countdown for resend code button
 */
function startResendCountdown() {
  const countdownElement = document.querySelector("#countdown-timer");
  const resendButton = document.querySelector("#resend-code");

  if (!countdownElement || !resendButton) return;

  let seconds = 60;
  countdownElement.textContent = seconds;

  resendButton.classList.add("disabled");

  const countdownInterval = setInterval(function () {
    seconds--;
    countdownElement.textContent = seconds;

    if (seconds <= 0) {
      clearInterval(countdownInterval);
      resendButton.classList.remove("disabled");
    }
  }, 1000);
}

/**
 * Validate email format
 */
function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

/**
 * Validate phone number format
 */
function validatePhone(phone) {
  const re = /^[0-9]{10,11}$/;
  return re.test(phone);
}

/**
 * Show notification message
 */
function showNotification(message, type) {
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
      <span>${message}</span>
    </div>
  `;

  document.body.appendChild(notification);

  // Auto remove after 3 seconds
  setTimeout(() => {
    notification.classList.add('fade-out');
    setTimeout(() => notification.remove(), 300);
  }, 3000);
}

/**
 * Simulate API call
 */
function simulateApiCall(data, callback) {
  // Simulate network delay
  setTimeout(() => {
    // For demo purposes, always return success
    callback({ success: true });
  }, 1000);
}

/**
 * Update password strength meter
 */
function updatePasswordStrength(e) {
  const password = e.target.value;
  const strengthMeter = document.querySelector('.password-strength-meter');
  const strengthText = document.querySelector('.password-strength-text');
  
  if (!strengthMeter || !strengthText) return;

  let strength = 0;
  
  // Length check
  if (password.length >= 8) strength += 1;
  
  // Contains number
  if (/\d/.test(password)) strength += 1;
  
  // Contains letter
  if (/[a-zA-Z]/.test(password)) strength += 1;
  
  // Contains special character
  if (/[^A-Za-z0-9]/.test(password)) strength += 1;

  // Update UI
  strengthMeter.className = 'password-strength-meter';
  switch(strength) {
    case 0:
      strengthMeter.classList.add('weak');
      strengthText.textContent = 'Yếu';
      break;
    case 1:
    case 2:
      strengthMeter.classList.add('medium');
      strengthText.textContent = 'Trung bình';
      break;
    case 3:
    case 4:
      strengthMeter.classList.add('strong');
      strengthText.textContent = 'Mạnh';
      break;
  }
}

/**
 * Initialize password toggle functionality
 */
function initPasswordToggle() {
  const toggleBtns = document.querySelectorAll('.toggle-password');
  
  toggleBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const input = this.previousElementSibling;
      const icon = this.querySelector('i');
      
      if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    });
  });
}
