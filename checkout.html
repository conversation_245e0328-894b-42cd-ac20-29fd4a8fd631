<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Thanh toán - ElectroDrive</title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/checkout.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header -->
    <header>
      <div class="container">
        <div class="logo">
          <a href="index.html"><h1>ElectroDrive</h1></a>
        </div>
        <nav>
          <ul class="menu">
            <li><a href="index.html">Trang chủ</a></li>
            <li><a href="products.html">Sản phẩm</a></li>
            <li><a href="index.html#about">Giới thiệu</a></li>
            <li><a href="index.html#contact">Liên hệ</a></li>
          </ul>
        </nav>
        <div class="header-icons">
          <a href="#" class="search-icon"><i class="fas fa-search"></i></a>
          <a href="cart.html" class="cart-icon"
            ><i class="fas fa-shopping-cart"></i
            ><span class="cart-count">0</span></a
          >
          <div class="mobile-menu-btn">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </header>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container">
      <div class="container">
        <div class="breadcrumb">
          <a href="index.html">Trang chủ</a>
          <span class="separator">/</span>
          <a href="cart.html">Giỏ hàng</a>
          <span class="separator">/</span>
          <span class="current">Thanh toán</span>
        </div>
      </div>
    </div>

    <!-- Checkout Section -->
    <section class="checkout-section">
      <div class="container">
        <h1 class="page-title">Thanh toán</h1>

        <div class="checkout-container">
          <!-- Checkout Form -->
          <div class="checkout-form-container">
            <form id="checkout-form">
              <!-- Customer Information -->
              <div class="form-section">
                <h2 class="section-title">Thông tin khách hàng</h2>

                <div class="form-row">
                  <div class="form-group">
                    <label for="fullname"
                      >Họ và tên <span class="required">*</span></label
                    >
                    <input type="text" id="fullname" name="fullname" required />
                  </div>
                  <div class="form-group">
                    <label for="email"
                      >Email <span class="required">*</span></label
                    >
                    <input type="email" id="email" name="email" required />
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="phone"
                      >Số điện thoại <span class="required">*</span></label
                    >
                    <input type="tel" id="phone" name="phone" required />
                  </div>
                  <div class="form-group">
                    <label for="company">Công ty (không bắt buộc)</label>
                    <input type="text" id="company" name="company" />
                  </div>
                </div>
              </div>

              <!-- Shipping Information -->
              <div class="form-section">
                <h2 class="section-title">Thông tin giao hàng</h2>

                <div class="form-group">
                  <label for="address"
                    >Địa chỉ <span class="required">*</span></label
                  >
                  <input type="text" id="address" name="address" required />
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="city"
                      >Tỉnh/Thành phố <span class="required">*</span></label
                    >
                    <select id="city" name="city" required>
                      <option value="">Chọn Tỉnh/Thành phố</option>
                      <option value="hanoi">Hà Nội</option>
                      <option value="hochiminh">TP. Hồ Chí Minh</option>
                      <option value="danang">Đà Nẵng</option>
                      <option value="haiphong">Hải Phòng</option>
                      <option value="cantho">Cần Thơ</option>
                      <!-- More cities would be added here -->
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="district"
                      >Quận/Huyện <span class="required">*</span></label
                    >
                    <select id="district" name="district" required disabled>
                      <option value="">Chọn Quận/Huyện</option>
                      <!-- Districts will be populated based on selected city -->
                    </select>
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label for="ward"
                      >Phường/Xã <span class="required">*</span></label
                    >
                    <select id="ward" name="ward" required disabled>
                      <option value="">Chọn Phường/Xã</option>
                      <!-- Wards will be populated based on selected district -->
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="zipcode">Mã bưu điện (không bắt buộc)</label>
                    <input type="text" id="zipcode" name="zipcode" />
                  </div>
                </div>

                <div class="form-group">
                  <label for="notes">Ghi chú đơn hàng (không bắt buộc)</label>
                  <textarea
                    id="notes"
                    name="notes"
                    rows="4"
                    placeholder="Ghi chú về đơn hàng, ví dụ: thời gian hay chỉ dẫn địa điểm giao hàng chi tiết hơn."
                  ></textarea>
                </div>
              </div>

              <!-- Payment Method -->
              <div class="form-section">
                <h2 class="section-title">Phương thức thanh toán</h2>

                <div class="payment-methods">
                  <div class="payment-method">
                    <input
                      type="radio"
                      id="payment-cod"
                      name="payment_method"
                      value="cod"
                      checked
                    />
                    <label for="payment-cod">
                      <span class="radio-button"></span>
                      <span class="method-name"
                        >Thanh toán khi nhận hàng (COD)</span
                      >
                      <span class="method-description"
                        >Thanh toán bằng tiền mặt khi nhận hàng</span
                      >
                    </label>
                  </div>

                  <div class="payment-method">
                    <input
                      type="radio"
                      id="payment-bank"
                      name="payment_method"
                      value="bank"
                    />
                    <label for="payment-bank">
                      <span class="radio-button"></span>
                      <span class="method-name">Chuyển khoản ngân hàng</span>
                      <span class="method-description"
                        >Thực hiện thanh toán vào tài khoản ngân hàng của chúng
                        tôi. Vui lòng sử dụng Mã đơn hàng của bạn trong phần Nội
                        dung thanh toán. Đơn hàng sẽ được giao sau khi tiền đã
                        chuyển.</span
                      >
                    </label>
                    <div class="bank-details" style="display: none;">
                      <p><strong>Thông tin tài khoản:</strong></p>
                      <p>Ngân hàng: Vietcombank</p>
                      <p>Số tài khoản: **********</p>
                      <p>Chủ tài khoản: CÔNG TY TNHH ELECTRODRIVE</p>
                      <p>Chi nhánh: Hà Nội</p>
                    </div>
                  </div>

                  <div class="payment-method">
                    <input
                      type="radio"
                      id="payment-credit"
                      name="payment_method"
                      value="credit"
                    />
                    <label for="payment-credit">
                      <span class="radio-button"></span>
                      <span class="method-name">Thẻ tín dụng/Thẻ ghi nợ</span>
                      <span class="method-description"
                        >Thanh toán an toàn với thẻ tín dụng/thẻ ghi nợ của
                        bạn</span
                      >
                    </label>
                    <div class="credit-card-form" style="display: none;">
                      <div class="form-row">
                        <div class="form-group">
                          <label for="card-number"
                            >Số thẻ <span class="required">*</span></label
                          >
                          <input
                            type="text"
                            id="card-number"
                            name="card_number"
                            placeholder="XXXX XXXX XXXX XXXX"
                          />
                        </div>
                      </div>
                      <div class="form-row">
                        <div class="form-group">
                          <label for="card-name"
                            >Tên chủ thẻ <span class="required">*</span></label
                          >
                          <input type="text" id="card-name" name="card_name" />
                        </div>
                      </div>
                      <div class="form-row">
                        <div class="form-group">
                          <label for="card-expiry"
                            >Ngày hết hạn <span class="required">*</span></label
                          >
                          <input
                            type="text"
                            id="card-expiry"
                            name="card_expiry"
                            placeholder="MM/YY"
                          />
                        </div>
                        <div class="form-group">
                          <label for="card-cvv"
                            >Mã bảo mật (CVV)
                            <span class="required">*</span></label
                          >
                          <input
                            type="text"
                            id="card-cvv"
                            name="card_cvv"
                            placeholder="XXX"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="payment-method">
                    <input
                      type="radio"
                      id="payment-momo"
                      name="payment_method"
                      value="momo"
                    />
                    <label for="payment-momo">
                      <span class="radio-button"></span>
                      <span class="method-name">Ví MoMo</span>
                      <span class="method-description"
                        >Thanh toán qua ví điện tử MoMo</span
                      >
                    </label>
                  </div>
                </div>
              </div>

              <div class="form-actions">
                <button type="submit" class="btn btn-primary place-order-btn">
                  Đặt hàng
                </button>
                <a href="cart.html" class="btn btn-outline return-to-cart"
                  >Quay lại giỏ hàng</a
                >
              </div>
            </form>
          </div>

          <!-- Order Summary -->
          <div class="order-summary">
            <h2>Đơn hàng của bạn</h2>

            <div class="order-details">
              <div class="order-header">
                <span>Sản phẩm</span>
                <span>Thành tiền</span>
              </div>

              <div id="order-items">
                <!-- Order items will be dynamically added here -->
              </div>

              <div class="order-subtotal">
                <span>Tạm tính:</span>
                <span id="order-subtotal">0 ₫</span>
              </div>

              <div class="order-shipping">
                <span>Phí vận chuyển:</span>
                <span id="order-shipping">0 ₫</span>
              </div>

              <div class="order-discount">
                <span>Giảm giá:</span>
                <span id="order-discount">0 ₫</span>
              </div>

              <div class="order-total">
                <span>Tổng cộng:</span>
                <span id="order-total">0 ₫</span>
              </div>
            </div>

            <div class="secure-checkout">
              <div class="secure-icon">
                <i class="fas fa-lock"></i>
              </div>
              <p>
                Thông tin thanh toán của bạn được bảo mật. Chúng tôi không lưu
                trữ thông tin thẻ tín dụng và không chia sẻ thông tin cá nhân
                của bạn.
              </p>
            </div>

            <div class="support-info">
              <h3>Cần hỗ trợ?</h3>
              <p>Gọi cho chúng tôi: <strong>1900 1234</strong></p>
              <p>Email: <strong><EMAIL></strong></p>
              <p>Giờ làm việc: 8:00 - 20:00, Thứ 2 - Chủ nhật</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-column">
            <div class="footer-logo">
              <h2>ElectroDrive</h2>
            </div>
            <p>
              Tiên phong trong lĩnh vực xe điện với sứ mệnh mang đến những sản
              phẩm chất lượng cao, thân thiện với môi trường và công nghệ tiên
              tiến.
            </p>
            <div class="social-icons">
              <a href="#"><i class="fab fa-facebook-f"></i></a>
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
          </div>
          <div class="footer-column">
            <h3>Liên kết nhanh</h3>
            <ul class="footer-links">
              <li><a href="index.html">Trang chủ</a></li>
              <li><a href="products.html">Sản phẩm</a></li>
              <li><a href="index.html#about">Giới thiệu</a></li>
              <li><a href="index.html#contact">Liên hệ</a></li>
              <li><a href="#">Tin tức</a></li>
              <li><a href="#">Chính sách bảo hành</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h3>Sản phẩm</h3>
            <ul class="footer-links">
              <li><a href="#">Xe điện Sedan</a></li>
              <li><a href="#">Xe điện SUV</a></li>
              <li><a href="#">Xe điện Sport</a></li>
              <li><a href="#">Xe điện Hatchback</a></li>
              <li><a href="#">Phụ kiện</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h3>Hỗ trợ</h3>
            <ul class="footer-links">
              <li><a href="#">Trung tâm hỗ trợ</a></li>
              <li><a href="#">Câu hỏi thường gặp</a></li>
              <li><a href="#">Hướng dẫn mua hàng</a></li>
              <li><a href="#">Chính sách vận chuyển</a></li>
              <li><a href="#">Chính sách đổi trả</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 ElectroDrive. Tất cả quyền được bảo lưu.</p>
          <div class="payment-methods">
            <img src="images/payment-visa.png" alt="Visa" />
            <img src="images/payment-mastercard.png" alt="Mastercard" />
            <img src="images/payment-paypal.png" alt="PayPal" />
          </div>
        </div>
      </div>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top"><i class="fas fa-arrow-up"></i></a>

    <!-- Order Item Template (hidden) -->
    <template id="order-item-template">
      <div class="order-item">
        <div class="item-details">
          <span class="item-name"></span>
          <span class="item-variant"
            >Màu: <span class="item-color"></span
          ></span>
          <span class="item-quantity"
            >x <span class="quantity-value"></span
          ></span>
        </div>
        <span class="item-total"></span>
      </div>
    </template>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/checkout.js"></script>
  </body>
</html>
