/* Checkout Page Styles */

/* Breadcrumb Styles */
.breadcrumb-container {
  background-color: #f8f9fa;
  padding: 15px 0;
  margin-bottom: 30px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.breadcrumb a {
  color: #333;
  text-decoration: none;
}

.breadcrumb a:hover {
  color: #4caf50;
}

.breadcrumb .separator {
  margin: 0 10px;
  color: #999;
}

.breadcrumb .current {
  color: #4caf50;
  font-weight: 500;
}

/* Page Title */
.page-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
  color: #333;
  text-align: center;
}

/* Checkout Container */
.checkout-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin-bottom: 60px;
}

.checkout-form-container {
  flex: 1 1 60%;
}

.order-summary {
  flex: 1 1 30%;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 25px;
  position: sticky;
  top: 20px;
  align-self: flex-start;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Form Sections */
.form-section {
  margin-bottom: 40px;
  background-color: #fff;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  color: #333;
}

/* Form Elements */
.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1 1 calc(50% - 10px);
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group .required {
  color: #e53935;
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
select,
textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 15px;
  transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus,
select:focus,
textarea:focus {
  border-color: #4caf50;
  outline: none;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

select {
  appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg fill="%23333" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 20px;
}

select:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

textarea {
  resize: vertical;
  min-height: 100px;
}

/* Payment Methods */
.payment-methods {
  margin-top: 20px;
}

.payment-method {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: all 0.3s;
}

.payment-method:hover {
  border-color: #4caf50;
}

.payment-method input[type="radio"] {
  display: none;
}

.payment-method label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
}

.radio-button {
  position: relative;
  width: 20px;
  height: 20px;
  border: 2px solid #999;
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
}

.payment-method input[type="radio"]:checked + label .radio-button {
  border-color: #4caf50;
}

.payment-method input[type="radio"]:checked + label .radio-button:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background-color: #4caf50;
  border-radius: 50%;
}

.method-name {
  font-weight: 600;
  margin-bottom: 5px;
  display: block;
}

.method-description {
  font-size: 14px;
  color: #666;
  display: block;
}

.bank-details,
.credit-card-form {
  margin-top: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.place-order-btn {
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
}

.return-to-cart {
  padding: 15px 20px;
  font-size: 16px;
}

/* Order Summary */
.order-summary h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.order-details {
  margin-bottom: 25px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  margin-bottom: 15px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.item-details {
  display: flex;
  flex-direction: column;
}

.item-name {
  font-weight: 500;
}

.item-variant,
.item-quantity {
  font-size: 14px;
  color: #666;
  margin-top: 3px;
}

.item-total {
  font-weight: 500;
}

.order-subtotal,
.order-shipping,
.order-discount {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.order-total {
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
  margin-top: 10px;
  border-top: 2px solid #ddd;
  font-weight: 700;
  font-size: 18px;
}

/* Secure Checkout */
.secure-checkout {
  display: flex;
  align-items: center;
  background-color: #e8f5e9;
  padding: 15px;
  border-radius: 4px;
  margin: 25px 0;
}

.secure-icon {
  font-size: 24px;
  color: #4caf50;
  margin-right: 15px;
}

.secure-checkout p {
  font-size: 14px;
  color: #333;
  margin: 0;
}

/* Support Info */
.support-info {
  background-color: #f1f8e9;
  padding: 15px;
  border-radius: 4px;
}

.support-info h3 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.support-info p {
  font-size: 14px;
  margin-bottom: 5px;
  color: #555;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .checkout-container {
    flex-direction: column;
  }

  .order-summary {
    position: static;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-group {
    flex: 1 1 100%;
  }

  .form-actions {
    flex-direction: column;
    gap: 15px;
  }

  .place-order-btn,
  .return-to-cart {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .page-title {
    font-size: 24px;
  }

  .section-title {
    font-size: 18px;
  }

  .form-section,
  .order-summary {
    padding: 15px;
  }

  .payment-method label {
    flex-direction: column;
  }

  .radio-button {
    margin-bottom: 10px;
  }
}
