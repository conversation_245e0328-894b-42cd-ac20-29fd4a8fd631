/**
 * Addresses JavaScript
 * Xử lý các chức năng tương tác trên trang quản lý địa chỉ
 */

document.addEventListener("DOMContentLoaded", function () {
  // Khởi tạo trang địa chỉ
  initAddressesPage();
});

/**
 * Khởi tạo trang địa chỉ
 */
function initAddressesPage() {
  // Kiểm tra trạng thái đăng nhập
  checkLoginStatus();

  // Tải dữ liệu địa chỉ
  loadAddresses();

  // Khởi tạo modal thêm/sửa địa chỉ
  initAddressFormModal();

  // Khởi tạo modal xóa địa chỉ
  initDeleteAddressModal();

  // Khởi tạo nút đăng xuất
  initLogout();
}

/**
 * Kiểm tra trạng thái đăng nhập
 */
function checkLoginStatus() {
  const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";

  if (!isLoggedIn) {
    // Chuyển hướng đến trang đăng nhập nếu chưa đăng nhập
    window.location.href = "login.html?redirect=addresses.html";
    return;
  }

  // Hiển thị thông tin người dùng
  displayUserInfo();
}

/**
 * Hiển thị thông tin người dùng
 */
function displayUserInfo() {
  const userData = JSON.parse(localStorage.getItem("userData")) || {};
  const userName = userData.firstName
    ? `${userData.firstName} ${userData.lastName || ""}`
    : "Người dùng ElectroDrive";
  const userEmail = userData.email || "<EMAIL>";
  const userAvatar = userData.avatar || "images/avatar-placeholder.jpg";

  // Cập nhật thông tin trong header
  document.getElementById("user-name").textContent = userName;
  document.getElementById("user-email").textContent = userEmail;
  document.getElementById("user-avatar").src = userAvatar;

  // Cập nhật thông tin trong sidebar
  document.getElementById("sidebar-name").textContent = userName;
  document.getElementById("sidebar-email").textContent = userEmail;
  document.getElementById("sidebar-avatar").src = userAvatar;
}

/**
 * Tải dữ liệu địa chỉ
 */
function loadAddresses() {
  // Lấy dữ liệu địa chỉ từ localStorage
  const userData = JSON.parse(localStorage.getItem("userData")) || {};
  const addresses = userData.addresses || [];

  // Hiển thị danh sách địa chỉ
  displayAddresses(addresses);
}

/**
 * Hiển thị danh sách địa chỉ
 * @param {Array} addresses - Mảng các địa chỉ
 */
function displayAddresses(addresses) {
  const addressesContainer = document.getElementById("addresses-container");
  const emptyAddressesElement = document.getElementById("empty-addresses");

  // Xóa nội dung hiện tại
  addressesContainer.innerHTML = "";

  // Kiểm tra nếu không có địa chỉ
  if (!addresses || addresses.length === 0) {
    emptyAddressesElement.style.display = "block";
    return;
  }

  // Ẩn trạng thái trống
  emptyAddressesElement.style.display = "none";

  // Tạo HTML cho mỗi địa chỉ
  addresses.forEach((address) => {
    const addressCard = createAddressCard(address);
    addressesContainer.appendChild(addressCard);
  });
}

/**
 * Tạo thẻ địa chỉ
 * @param {Object} address - Thông tin địa chỉ
 * @returns {HTMLElement} - Phần tử HTML của thẻ địa chỉ
 */
function createAddressCard(address) {
  const addressCard = document.createElement("div");
  addressCard.className = `address-card ${address.isDefault ? "default" : ""}`;
  addressCard.dataset.id = address.id;

  // Tạo badge mặc định nếu cần
  if (address.isDefault) {
    const defaultBadge = document.createElement("div");
    defaultBadge.className = "default-badge";
    defaultBadge.textContent = "Mặc định";
    addressCard.appendChild(defaultBadge);
  }

  // Tạo loại địa chỉ
  const addressType = document.createElement("div");
  addressType.className = `address-type ${address.type}`;
  addressType.textContent = getAddressTypeText(address.type);

  // Tạo tên và số điện thoại
  const addressName = document.createElement("div");
  addressName.className = "address-name";
  addressName.textContent = address.fullName;

  const addressPhone = document.createElement("div");
  addressPhone.className = "address-phone";
  addressPhone.textContent = address.phone;

  // Tạo chi tiết địa chỉ
  const addressDetails = document.createElement("div");
  addressDetails.className = "address-details";
  addressDetails.innerHTML = `
        ${address.address}<br>
        ${address.ward}, ${address.district}<br>
        ${address.province}
    `;

  // Tạo hàng các nút hành động
  const actionsRow = document.createElement("div");
  actionsRow.className = "address-actions-row";

  // Nút đặt làm mặc định (chỉ hiển thị nếu không phải địa chỉ mặc định)
  if (!address.isDefault) {
    const setDefaultBtn = document.createElement("button");
    setDefaultBtn.className = "set-default-btn";
    setDefaultBtn.innerHTML =
      '<i class="fas fa-check-circle"></i> Đặt làm mặc định';
    setDefaultBtn.addEventListener("click", function () {
      setDefaultAddress(address.id);
    });
    actionsRow.appendChild(setDefaultBtn);
  }

  // Nút chỉnh sửa
  const editBtn = document.createElement("button");
  editBtn.className = "edit-address-btn";
  editBtn.innerHTML = '<i class="fas fa-edit"></i> Sửa';
  editBtn.addEventListener("click", function () {
    editAddress(address.id);
  });
  actionsRow.appendChild(editBtn);

  // Nút xóa (chỉ hiển thị nếu không phải địa chỉ mặc định hoặc nếu có nhiều hơn 1 địa chỉ)
  const addresses =
    JSON.parse(localStorage.getItem("userData"))?.addresses || [];
  if (!address.isDefault || addresses.length > 1) {
    const deleteBtn = document.createElement("button");
    deleteBtn.className = "delete-address-btn";
    deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i> Xóa';
    deleteBtn.addEventListener("click", function () {
      openDeleteAddressModal(address.id);
    });
    actionsRow.appendChild(deleteBtn);
  }

  // Thêm tất cả các phần tử vào thẻ
  addressCard.appendChild(addressType);
  addressCard.appendChild(addressName);
  addressCard.appendChild(addressPhone);
  addressCard.appendChild(addressDetails);
  addressCard.appendChild(actionsRow);

  return addressCard;
}

/**
 * Khởi tạo modal thêm/sửa địa chỉ
 */
function initAddressFormModal() {
  const modal = document.getElementById("address-form-modal");
  const closeButton = modal.querySelector(".modal-close");
  const cancelButton = document.getElementById("cancel-address-btn");
  const form = document.getElementById("address-form");
  const addAddressBtn = document.getElementById("add-address-btn");
  const emptyAddAddressBtn = document.getElementById("empty-add-address-btn");

  // Thêm sự kiện click cho nút thêm địa chỉ
  addAddressBtn.addEventListener("click", function () {
    openAddressFormModal();
  });

  // Thêm sự kiện click cho nút thêm địa chỉ khi không có địa chỉ nào
  emptyAddAddressBtn.addEventListener("click", function () {
    openAddressFormModal();
  });

  // Thêm sự kiện click cho nút đóng
  closeButton.addEventListener("click", function () {
    closeModal(modal);
  });

  // Thêm sự kiện click cho nút hủy
  cancelButton.addEventListener("click", function () {
    closeModal(modal);
  });

  // Thêm sự kiện click bên ngoài modal để đóng
  window.addEventListener("click", function (event) {
    if (event.target === modal) {
      closeModal(modal);
    }
  });

  // Thêm sự kiện submit cho form
  form.addEventListener("submit", function (e) {
    e.preventDefault();
    saveAddress();
  });

  // Khởi tạo các bộ chọn địa điểm
  initLocationSelectors();
}

/**
 * Khởi tạo các bộ chọn địa điểm (Tỉnh/Thành phố, Quận/Huyện, Phường/Xã)
 */
function initLocationSelectors() {
  const provinceSelect = document.getElementById("address-province");
  const districtSelect = document.getElementById("address-district");
  const wardSelect = document.getElementById("address-ward");

  // Tải dữ liệu tỉnh/thành phố
  loadProvinces(provinceSelect);

  // Thêm sự kiện thay đổi cho bộ chọn tỉnh/thành phố
  provinceSelect.addEventListener("change", function () {
    const provinceId = this.value;
    if (provinceId) {
      loadDistricts(districtSelect, provinceId);
      districtSelect.disabled = false;
      wardSelect.disabled = true;
      wardSelect.innerHTML = '<option value="">Chọn Phường/Xã</option>';
    } else {
      districtSelect.disabled = true;
      wardSelect.disabled = true;
      districtSelect.innerHTML = '<option value="">Chọn Quận/Huyện</option>';
      wardSelect.innerHTML = '<option value="">Chọn Phường/Xã</option>';
    }
  });

  // Thêm sự kiện thay đổi cho bộ chọn quận/huyện
  districtSelect.addEventListener("change", function () {
    const districtId = this.value;
    if (districtId) {
      loadWards(wardSelect, districtId);
      wardSelect.disabled = false;
    } else {
      wardSelect.disabled = true;
      wardSelect.innerHTML = '<option value="">Chọn Phường/Xã</option>';
    }
  });
}

/**
 * Tải dữ liệu tỉnh/thành phố
 * @param {HTMLElement} selectElement - Phần tử select
 */
function loadProvinces(selectElement) {
  // Dữ liệu mẫu cho tỉnh/thành phố
  const provinces = [
    { id: "01", name: "Hà Nội" },
    { id: "02", name: "TP. Hồ Chí Minh" },
    { id: "03", name: "Đà Nẵng" },
    { id: "04", name: "Hải Phòng" },
    { id: "05", name: "Cần Thơ" },
    { id: "06", name: "An Giang" },
    { id: "07", name: "Bà Rịa - Vũng Tàu" },
    { id: "08", name: "Bắc Giang" },
    { id: "09", name: "Bắc Kạn" },
    { id: "10", name: "Bạc Liêu" },
  ];

  // Xóa các option hiện tại
  selectElement.innerHTML = '<option value="">Chọn Tỉnh/Thành phố</option>';

  // Thêm các option mới
  provinces.forEach((province) => {
    const option = document.createElement("option");
    option.value = province.id;
    option.textContent = province.name;
    selectElement.appendChild(option);
  });
}

/**
 * Tải dữ liệu quận/huyện
 * @param {HTMLElement} selectElement - Phần tử select
 * @param {string} provinceId - ID tỉnh/thành phố
 */
function loadDistricts(selectElement, provinceId) {
  // Dữ liệu mẫu cho quận/huyện
  const districts = {
    "01": [
      // Hà Nội
      { id: "001", name: "Ba Đình" },
      { id: "002", name: "Hoàn Kiếm" },
      { id: "003", name: "Tây Hồ" },
      { id: "004", name: "Long Biên" },
      { id: "005", name: "Cầu Giấy" },
      { id: "006", name: "Đống Đa" },
      { id: "007", name: "Hai Bà Trưng" },
      { id: "008", name: "Hoàng Mai" },
      { id: "009", name: "Thanh Xuân" },
      { id: "010", name: "Hà Đông" },
    ],
    "02": [
      // TP. Hồ Chí Minh
      { id: "001", name: "Quận 1" },
      { id: "002", name: "Quận 3" },
      { id: "003", name: "Quận 4" },
      { id: "004", name: "Quận 5" },
      { id: "005", name: "Quận 6" },
      { id: "006", name: "Quận 7" },
      { id: "007", name: "Quận 8" },
      { id: "008", name: "Quận 10" },
      { id: "009", name: "Quận 11" },
      { id: "010", name: "Quận 12" },
    ],
    "03": [
      // Đà Nẵng
      { id: "001", name: "Hải Châu" },
      { id: "002", name: "Thanh Khê" },
      { id: "003", name: "Sơn Trà" },
      { id: "004", name: "Ngũ Hành Sơn" },
      { id: "005", name: "Liên Chiểu" },
      { id: "006", name: "Cẩm Lệ" },
      { id: "007", name: "Hòa Vang" },
    ],
  };

  // Xóa các option hiện tại
  selectElement.innerHTML = '<option value="">Chọn Quận/Huyện</option>';

  // Thêm các option mới
  if (districts[provinceId]) {
    districts[provinceId].forEach((district) => {
      const option = document.createElement("option");
      option.value = district.id;
      option.textContent = district.name;
      selectElement.appendChild(option);
    });
  }
}

/**
 * Tải dữ liệu phường/xã
 * @param {HTMLElement} selectElement - Phần tử select
 * @param {string} districtId - ID quận/huyện
 */
function loadWards(selectElement, districtId) {
  // Dữ liệu mẫu cho phường/xã
  const wards = {
    "001": [
      // Ba Đình hoặc Quận 1 hoặc Hải Châu
      { id: "00001", name: "Phường 1" },
      { id: "00002", name: "Phường 2" },
      { id: "00003", name: "Phường 3" },
      { id: "00004", name: "Phường 4" },
      { id: "00005", name: "Phường 5" },
    ],
    "002": [
      // Hoàn Kiếm hoặc Quận 3 hoặc Thanh Khê
      { id: "00001", name: "Phường 6" },
      { id: "00002", name: "Phường 7" },
      { id: "00003", name: "Phường 8" },
      { id: "00004", name: "Phường 9" },
      { id: "00005", name: "Phường 10" },
    ],
  };

  // Xóa các option hiện tại
  selectElement.innerHTML = '<option value="">Chọn Phường/Xã</option>';

  // Thêm các option mới
  if (wards[districtId]) {
    wards[districtId].forEach((ward) => {
      const option = document.createElement("option");
      option.value = ward.id;
      option.textContent = ward.name;
      selectElement.appendChild(option);
    });
  } else {
    // Nếu không có dữ liệu cho quận/huyện này, thêm một số phường/xã mẫu
    for (let i = 1; i <= 5; i++) {
      const option = document.createElement("option");
      option.value = `${districtId}${i}`;
      option.textContent = `Phường/Xã ${i}`;
      selectElement.appendChild(option);
    }
  }
}

/**
 * Mở modal thêm/sửa địa chỉ
 * @param {string} addressId - ID địa chỉ (nếu là sửa địa chỉ)
 */
function openAddressFormModal(addressId = null) {
  const modal = document.getElementById("address-form-modal");
  const form = document.getElementById("address-form");
  const titleElement = document.getElementById("address-form-title");

  // Reset form
  form.reset();

  // Đặt tiêu đề modal
  if (addressId) {
    titleElement.textContent = "Chỉnh sửa địa chỉ";
  } else {
    titleElement.textContent = "Thêm địa chỉ mới";
  }

  // Đặt ID địa chỉ (nếu có)
  document.getElementById("address-id").value = addressId || "";

  // Nếu là sửa địa chỉ, điền thông tin vào form
  if (addressId) {
    fillAddressForm(addressId);
  }

  // Hiển thị modal
  modal.style.display = "block";
  document.body.style.overflow = "hidden"; // Ngăn cuộn trang khi modal hiển thị
}

/**
 * Điền thông tin địa chỉ vào form
 * @param {string} addressId - ID địa chỉ
 */
function fillAddressForm(addressId) {
  const userData = JSON.parse(localStorage.getItem("userData")) || {};
  const addresses = userData.addresses || [];
  const address = addresses.find((addr) => addr.id === addressId);

  if (!address) {
    showNotification("Không tìm thấy thông tin địa chỉ", "error");
    return;
  }

  // Điền thông tin vào form
  document.getElementById("address-fullname").value = address.fullName;
  document.getElementById("address-phone").value = address.phone;
  document.getElementById("address-line").value = address.address;
  document.getElementById("address-type").value = address.type;
  document.getElementById("address-default").checked = address.isDefault;

  // Điền thông tin tỉnh/thành phố, quận/huyện, phường/xã
  const provinceSelect = document.getElementById("address-province");
  const districtSelect = document.getElementById("address-district");
  const wardSelect = document.getElementById("address-ward");

  // Tìm và chọn tỉnh/thành phố
  for (let i = 0; i < provinceSelect.options.length; i++) {
    if (provinceSelect.options[i].text === address.province) {
      provinceSelect.selectedIndex = i;
      break;
    }
  }

  // Tải quận/huyện dựa trên tỉnh/thành phố đã chọn
  if (provinceSelect.value) {
    loadDistricts(districtSelect, provinceSelect.value);
    districtSelect.disabled = false;

    // Tìm và chọn quận/huyện
    setTimeout(() => {
      for (let i = 0; i < districtSelect.options.length; i++) {
        if (districtSelect.options[i].text === address.district) {
          districtSelect.selectedIndex = i;
          break;
        }
      }

      // Tải phường/xã dựa trên quận/huyện đã chọn
      if (districtSelect.value) {
        loadWards(wardSelect, districtSelect.value);
        wardSelect.disabled = false;

        // Tìm và chọn phường/xã
        setTimeout(() => {
          for (let i = 0; i < wardSelect.options.length; i++) {
            if (wardSelect.options[i].text === address.ward) {
              wardSelect.selectedIndex = i;
              break;
            }
          }
        }, 100);
      }
    }, 100);
  }
}

/**
 * Lưu địa chỉ
 */
function saveAddress() {
  // Lấy dữ liệu từ form
  const addressId = document.getElementById("address-id").value;
  const fullName = document.getElementById("address-fullname").value.trim();
  const phone = document.getElementById("address-phone").value.trim();
  const addressLine = document.getElementById("address-line").value.trim();
  const provinceSelect = document.getElementById("address-province");
  const districtSelect = document.getElementById("address-district");
  const wardSelect = document.getElementById("address-ward");
  const addressType = document.getElementById("address-type").value;
  const isDefault = document.getElementById("address-default").checked;

  // Kiểm tra dữ liệu
  if (!fullName) {
    showNotification("Vui lòng nhập họ và tên", "error");
    return;
  }

  if (!phone) {
    showNotification("Vui lòng nhập số điện thoại", "error");
    return;
  }

  if (!validatePhone(phone)) {
    showNotification("Số điện thoại không hợp lệ", "error");
    return;
  }

  if (!addressLine) {
    showNotification("Vui lòng nhập địa chỉ", "error");
    return;
  }

  if (!provinceSelect.value) {
    showNotification("Vui lòng chọn Tỉnh/Thành phố", "error");
    return;
  }

  if (!districtSelect.value) {
    showNotification("Vui lòng chọn Quận/Huyện", "error");
    return;
  }

  if (!wardSelect.value) {
    showNotification("Vui lòng chọn Phường/Xã", "error");
    return;
  }

  // Lấy tên tỉnh/thành phố, quận/huyện, phường/xã
  const province = provinceSelect.options[provinceSelect.selectedIndex].text;
  const district = districtSelect.options[districtSelect.selectedIndex].text;
  const ward = wardSelect.options[wardSelect.selectedIndex].text;

  // Tạo đối tượng địa chỉ
  const address = {
    id: addressId || generateId(),
    fullName,
    phone,
    address: addressLine,
    province,
    district,
    ward,
    type: addressType,
    isDefault,
  };

  // Lưu địa chỉ vào localStorage
  saveAddressToLocalStorage(address);

  // Đóng modal
  const modal = document.getElementById("address-form-modal");
  closeModal(modal);

  // Hiển thị thông báo
  showNotification(
    addressId ? "Cập nhật địa chỉ thành công" : "Thêm địa chỉ mới thành công",
    "success"
  );

  // Tải lại danh sách địa chỉ
  loadAddresses();
}

/**
 * Lưu địa chỉ vào localStorage
 * @param {Object} address - Thông tin địa chỉ
 */
function saveAddressToLocalStorage(address) {
  // Lấy dữ liệu người dùng từ localStorage
  const userData = JSON.parse(localStorage.getItem("userData")) || {};
  let addresses = userData.addresses || [];

  // Nếu là địa chỉ mặc định, đặt tất cả các địa chỉ khác thành không mặc định
  if (address.isDefault) {
    addresses = addresses.map((addr) => ({
      ...addr,
      isDefault: false,
    }));
  }

  // Kiểm tra xem đã có địa chỉ nào chưa, nếu chưa có thì đặt địa chỉ đầu tiên làm mặc định
  if (addresses.length === 0) {
    address.isDefault = true;
  }

  // Kiểm tra xem địa chỉ đã tồn tại chưa
  const existingIndex = addresses.findIndex((addr) => addr.id === address.id);

  if (existingIndex !== -1) {
    // Cập nhật địa chỉ đã tồn tại
    addresses[existingIndex] = address;
  } else {
    // Thêm địa chỉ mới
    addresses.push(address);
  }

  // Cập nhật dữ liệu người dùng
  userData.addresses = addresses;
  localStorage.setItem("userData", JSON.stringify(userData));
}

/**
 * Chỉnh sửa địa chỉ
 * @param {string} addressId - ID địa chỉ
 */
function editAddress(addressId) {
  openAddressFormModal(addressId);
}

/**
 * Đặt địa chỉ làm mặc định
 * @param {string} addressId - ID địa chỉ
 */
function setDefaultAddress(addressId) {
  // Lấy dữ liệu người dùng từ localStorage
  const userData = JSON.parse(localStorage.getItem("userData")) || {};
  const addresses = userData.addresses || [];

  // Đặt tất cả các địa chỉ thành không mặc định
  const updatedAddresses = addresses.map((addr) => ({
    ...addr,
    isDefault: addr.id === addressId,
  }));

  // Cập nhật dữ liệu người dùng
  userData.addresses = updatedAddresses;
  localStorage.setItem("userData", JSON.stringify(userData));

  // Hiển thị thông báo
  showNotification("Đã đặt địa chỉ làm mặc định", "success");

  // Tải lại danh sách địa chỉ
  loadAddresses();
}

/**
 * Khởi tạo modal xóa địa chỉ
 */
function initDeleteAddressModal() {
  const modal = document.getElementById("delete-address-modal");
  const closeButton = modal.querySelector(".modal-close");
  const cancelButton = document.getElementById("cancel-delete-btn");
  const confirmButton = document.getElementById("confirm-delete-btn");

  // Thêm sự kiện click cho nút đóng
  closeButton.addEventListener("click", function () {
    closeModal(modal);
  });

  // Thêm sự kiện click cho nút hủy
  cancelButton.addEventListener("click", function () {
    closeModal(modal);
  });

  // Thêm sự kiện click bên ngoài modal để đóng
  window.addEventListener("click", function (event) {
    if (event.target === modal) {
      closeModal(modal);
    }
  });

  // Thêm sự kiện click cho nút xác nhận xóa
  confirmButton.addEventListener("click", function () {
    const addressId = this.dataset.addressId;
    deleteAddress(addressId);
    closeModal(modal);
  });
}

/**
 * Mở modal xóa địa chỉ
 * @param {string} addressId - ID địa chỉ
 */
function openDeleteAddressModal(addressId) {
  const modal = document.getElementById("delete-address-modal");
  const confirmButton = document.getElementById("confirm-delete-btn");

  // Đặt ID địa chỉ cho nút xác nhận
  confirmButton.dataset.addressId = addressId;

  // Hiển thị modal
  modal.style.display = "block";
  document.body.style.overflow = "hidden"; // Ngăn cuộn trang khi modal hiển thị
}

/**
 * Xóa địa chỉ
 * @param {string} addressId - ID địa chỉ
 */
function deleteAddress(addressId) {
  // Lấy dữ liệu người dùng từ localStorage
  const userData = JSON.parse(localStorage.getItem("userData")) || {};
  let addresses = userData.addresses || [];

  // Kiểm tra xem địa chỉ có phải là mặc định không
  const addressToDelete = addresses.find((addr) => addr.id === addressId);
  const isDefault = addressToDelete?.isDefault || false;

  // Xóa địa chỉ
  addresses = addresses.filter((addr) => addr.id !== addressId);

  // Nếu địa chỉ bị xóa là mặc định và còn địa chỉ khác, đặt địa chỉ đầu tiên làm mặc định
  if (isDefault && addresses.length > 0) {
    addresses[0].isDefault = true;
  }

  // Cập nhật dữ liệu người dùng
  userData.addresses = addresses;
  localStorage.setItem("userData", JSON.stringify(userData));

  // Hiển thị thông báo
  showNotification("Đã xóa địa chỉ thành công", "success");

  // Tải lại danh sách địa chỉ
  loadAddresses();
}

/**
 * Đóng modal
 * @param {HTMLElement} modal - Phần tử modal
 */
function closeModal(modal) {
  modal.style.display = "none";
  document.body.style.overflow = ""; // Khôi phục cuộn trang
}

/**
 * Khởi tạo nút đăng xuất
 */
function initLogout() {
  const logoutButton = document.getElementById("logout-button");
  const sidebarLogout = document.getElementById("sidebar-logout");

  logoutButton.addEventListener("click", logout);
  sidebarLogout.addEventListener("click", logout);
}

/**
 * Đăng xuất
 */
function logout() {
  // Xóa thông tin đăng nhập
  localStorage.removeItem("isLoggedIn");
  localStorage.removeItem("userData");

  // Chuyển hướng đến trang đăng nhập
  window.location.href = "login.html";
}

/**
 * Lấy văn bản loại địa chỉ
 * @param {string} type - Loại địa chỉ
 * @returns {string} - Văn bản loại địa chỉ
 */
function getAddressTypeText(type) {
  switch (type) {
    case "home":
      return "Nhà riêng";
    case "office":
      return "Văn phòng";
    case "other":
      return "Khác";
    default:
      return type;
  }
}

/**
 * Tạo ID ngẫu nhiên
 * @returns {string} - ID ngẫu nhiên
 */
function generateId() {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}

/**
 * Xác thực số điện thoại
 * @param {string} phone - Số điện thoại
 * @returns {boolean} - Kết quả xác thực
 */
function validatePhone(phone) {
  // Kiểm tra số điện thoại Việt Nam
  const phoneRegex = /(84|0[3|5|7|8|9])+([0-9]{8})\b/;
  return phoneRegex.test(phone);
}

/**
 * Hiển thị thông báo
 * @param {string} message - Nội dung thông báo
 * @param {string} type - Loại thông báo (success, error, info, warning)
 */
function showNotification(message, type = "info") {
  // Kiểm tra xem đã có container thông báo chưa
  let notificationContainer = document.querySelector(".notification-container");

  // Nếu chưa có, tạo mới
  if (!notificationContainer) {
    notificationContainer = document.createElement("div");
    notificationContainer.className = "notification-container";
    document.body.appendChild(notificationContainer);

    // Thêm CSS cho container
    notificationContainer.style.position = "fixed";
    notificationContainer.style.top = "20px";
    notificationContainer.style.right = "20px";
    notificationContainer.style.zIndex = "9999";
  }

  // Tạo thông báo
  const notification = document.createElement("div");
  notification.className = `notification notification-${type}`;

  // Thêm CSS cho thông báo
  notification.style.backgroundColor = "#fff";
  notification.style.borderRadius = "4px";
  notification.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.1)";
  notification.style.padding = "15px 20px";
  notification.style.marginBottom = "10px";
  notification.style.display = "flex";
  notification.style.alignItems = "center";
  notification.style.transition = "all 0.3s ease";
  notification.style.position = "relative";
  notification.style.borderLeft = "4px solid #ccc";

  // Đặt màu viền trái theo loại thông báo
  switch (type) {
    case "success":
      notification.style.borderLeftColor = "#4CAF50";
      break;
    case "error":
      notification.style.borderLeftColor = "#f44336";
      break;
    case "warning":
      notification.style.borderLeftColor = "#ff9800";
      break;
    case "info":
      notification.style.borderLeftColor = "#2196F3";
      break;
  }

  // Tạo icon theo loại thông báo
  let icon = "";
  switch (type) {
    case "success":
      icon =
        '<i class="fas fa-check-circle" style="color: #4CAF50; margin-right: 10px; font-size: 18px;"></i>';
      break;
    case "error":
      icon =
        '<i class="fas fa-times-circle" style="color: #f44336; margin-right: 10px; font-size: 18px;"></i>';
      break;
    case "warning":
      icon =
        '<i class="fas fa-exclamation-triangle" style="color: #ff9800; margin-right: 10px; font-size: 18px;"></i>';
      break;
    case "info":
      icon =
        '<i class="fas fa-info-circle" style="color: #2196F3; margin-right: 10px; font-size: 18px;"></i>';
      break;
  }

  // Tạo nút đóng
  const closeButton =
    '<button class="notification-close" style="background: none; border: none; color: #999; cursor: pointer; position: absolute; top: 10px; right: 10px; font-size: 14px;"><i class="fas fa-times"></i></button>';

  // Thêm nội dung vào thông báo
  notification.innerHTML = `${icon}<div>${message}</div>${closeButton}`;

  // Thêm thông báo vào container
  notificationContainer.appendChild(notification);

  // Thêm sự kiện click cho nút đóng
  notification
    .querySelector(".notification-close")
    .addEventListener("click", function () {
      notification.remove();
    });

  // Tự động đóng thông báo sau 5 giây
  setTimeout(() => {
    notification.style.opacity = "0";
    notification.style.transform = "translateX(20px)";

    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 5000);
}
