/**
 * Orders JavaScript
 * Xử lý các chức năng tương tác trên trang danh sách đơn hàng
 */

document.addEventListener("DOMContentLoaded", function () {
  // Khởi tạo trang đơn hàng
  initOrdersPage();
});

/**
 * Khởi tạo trang đơn hàng
 */
function initOrdersPage() {
  // Kiểm tra trạng thái đăng nhập
  checkLoginStatus();

  // Tải dữ liệu đơn hàng
  loadOrders();

  // Khởi tạo bộ lọc
  initFilters();

  // Khởi tạo phân trang
  initPagination();

  // Khởi tạo modal chi tiết đơn hàng
  initOrderDetailModal();

  // Khởi tạo modal hủy đơn hàng
  initCancelOrderModal();

  // Khởi tạo nút đăng xuất
  initLogout();
}

/**
 * Kiểm tra trạng thái đăng nhập
 */
function checkLoginStatus() {
  const isLoggedIn = localStorage.getItem("isLoggedIn") === "true";

  if (!isLoggedIn) {
    // Chuyển hướng đến trang đăng nhập nếu chưa đăng nhập
    window.location.href = "login.html?redirect=orders.html";
    return;
  }

  // Hiển thị thông tin người dùng
  displayUserInfo();
}

/**
 * Hiển thị thông tin người dùng
 */
function displayUserInfo() {
  const userData = JSON.parse(localStorage.getItem("userData")) || {};
  const userName = userData.firstName
    ? `${userData.firstName} ${userData.lastName || ""}`
    : "Người dùng ElectroDrive";
  const userEmail = userData.email || "<EMAIL>";
  const userAvatar = userData.avatar || "images/avatar-placeholder.jpg";

  // Cập nhật thông tin trong header
  document.getElementById("user-name").textContent = userName;
  document.getElementById("user-email").textContent = userEmail;
  document.getElementById("user-avatar").src = userAvatar;

  // Cập nhật thông tin trong sidebar
  document.getElementById("sidebar-name").textContent = userName;
  document.getElementById("sidebar-email").textContent = userEmail;
  document.getElementById("sidebar-avatar").src = userAvatar;
}

/**
 * Tải dữ liệu đơn hàng
 */
function loadOrders() {
  // Lấy dữ liệu đơn hàng từ localStorage
  const orders = JSON.parse(localStorage.getItem("orders")) || [];
  const userData = JSON.parse(localStorage.getItem("userData")) || {};
  const userEmail = userData.email;

  // Lọc đơn hàng của người dùng hiện tại
  const userOrders = userEmail
    ? orders.filter((order) => order.customer.email === userEmail)
    : [];

  // Hiển thị danh sách đơn hàng
  displayOrders(userOrders);
}

/**
 * Hiển thị danh sách đơn hàng
 * @param {Array} orders - Mảng các đơn hàng
 */
function displayOrders(orders) {
  const ordersContainer = document.getElementById("orders-container");
  const emptyOrdersElement = document.getElementById("empty-orders");

  // Xóa nội dung hiện tại
  ordersContainer.innerHTML = "";

  // Kiểm tra nếu không có đơn hàng
  if (!orders || orders.length === 0) {
    emptyOrdersElement.style.display = "block";
    return;
  }

  // Ẩn trạng thái trống
  emptyOrdersElement.style.display = "none";

  // Sắp xếp đơn hàng theo thời gian giảm dần (mới nhất lên đầu)
  const sortedOrders = [...orders].sort(
    (a, b) => new Date(b.orderDate) - new Date(a.orderDate)
  );

  // Tạo HTML cho mỗi đơn hàng
  sortedOrders.forEach((order) => {
    const orderCard = createOrderCard(order);
    ordersContainer.appendChild(orderCard);
  });
}

/**
 * Tạo thẻ đơn hàng
 * @param {Object} order - Thông tin đơn hàng
 * @returns {HTMLElement} - Phần tử HTML của thẻ đơn hàng
 */
function createOrderCard(order) {
  const orderCard = document.createElement("div");
  orderCard.className = "order-card";

  // Tạo header của đơn hàng
  const orderHeader = document.createElement("div");
  orderHeader.className = "order-header";
  orderHeader.innerHTML = `
        <div class="order-id">
            Đơn hàng #${order.orderId}
        </div>
        <div class="order-date">
            ${formatDate(order.orderDate)}
        </div>
        <div class="order-status status-${order.status.toLowerCase()}">
            ${getStatusText(order.status)}
        </div>
    `;

  // Tạo body của đơn hàng
  const orderBody = document.createElement("div");
  orderBody.className = "order-body";

  // Tạo danh sách sản phẩm
  const orderProducts = document.createElement("div");
  orderProducts.className = "order-products";

  // Hiển thị tối đa 5 sản phẩm
  const displayedProducts = order.items.slice(0, 5);
  displayedProducts.forEach((item) => {
    const productElement = document.createElement("div");
    productElement.className = "order-product";
    productElement.innerHTML = `
            <img src="${item.image || "images/product-placeholder.jpg"}" alt="${
      item.name
    }">
            ${
              item.quantity > 1
                ? `<span class="product-count">${item.quantity}</span>`
                : ""
            }
        `;
    orderProducts.appendChild(productElement);
  });

  // Nếu có nhiều hơn 5 sản phẩm, hiển thị số lượng còn lại
  if (order.items.length > 5) {
    const moreProducts = document.createElement("div");
    moreProducts.className = "order-product";
    moreProducts.innerHTML = `
            <div class="product-more">+${order.items.length - 5}</div>
        `;
    orderProducts.appendChild(moreProducts);
  }

  // Tạo phần tổng kết đơn hàng
  const orderSummary = document.createElement("div");
  orderSummary.className = "order-summary";
  orderSummary.innerHTML = `
        <div class="order-total">
            ${formatCurrency(order.total)}
        </div>
        <div class="order-actions">
            ${
              order.status === "pending" || order.status === "processing"
                ? `<button class="btn-cancel-order" data-order-id="${order.orderId}">Hủy đơn</button>`
                : ""
            }
            ${
              order.status === "shipping" || order.status === "completed"
                ? `<button class="btn-track-order" data-order-id="${order.orderId}">Theo dõi</button>`
                : ""
            }
            <button class="btn-view-order" data-order-id="${
              order.orderId
            }">Chi tiết</button>
        </div>
    `;

  // Thêm các phần tử vào body
  orderBody.appendChild(orderProducts);
  orderBody.appendChild(orderSummary);

  // Thêm header và body vào card
  orderCard.appendChild(orderHeader);
  orderCard.appendChild(orderBody);

  // Thêm sự kiện click cho nút xem chi tiết
  orderCard
    .querySelector(".btn-view-order")
    .addEventListener("click", function () {
      const orderId = this.getAttribute("data-order-id");
      openOrderDetail(orderId);
    });

  // Thêm sự kiện click cho nút theo dõi đơn hàng
  const trackButton = orderCard.querySelector(".btn-track-order");
  if (trackButton) {
    trackButton.addEventListener("click", function () {
      const orderId = this.getAttribute("data-order-id");
      trackOrder(orderId);
    });
  }

  // Thêm sự kiện click cho nút hủy đơn hàng
  const cancelButton = orderCard.querySelector(".btn-cancel-order");
  if (cancelButton) {
    cancelButton.addEventListener("click", function () {
      const orderId = this.getAttribute("data-order-id");
      openCancelOrderModal(orderId);
    });
  }

  return orderCard;
}

/**
 * Khởi tạo bộ lọc
 */
function initFilters() {
  const statusFilter = document.getElementById("order-status");
  const dateFilter = document.getElementById("order-date");
  const searchInput = document.getElementById("order-search");
  const searchButton = document.getElementById("search-btn");

  // Thêm sự kiện thay đổi cho bộ lọc trạng thái
  statusFilter.addEventListener("change", function () {
    applyFilters();
  });

  // Thêm sự kiện thay đổi cho bộ lọc thời gian
  dateFilter.addEventListener("change", function () {
    applyFilters();
  });

  // Thêm sự kiện click cho nút tìm kiếm
  searchButton.addEventListener("click", function () {
    applyFilters();
  });

  // Thêm sự kiện nhấn Enter cho ô tìm kiếm
  searchInput.addEventListener("keypress", function (e) {
    if (e.key === "Enter") {
      applyFilters();
    }
  });
}

/**
 * Áp dụng bộ lọc
 */
function applyFilters() {
  const statusFilter = document.getElementById("order-status").value;
  const dateFilter = document.getElementById("order-date").value;
  const searchQuery = document
    .getElementById("order-search")
    .value.trim()
    .toLowerCase();

  // Lấy dữ liệu đơn hàng từ localStorage
  const orders = JSON.parse(localStorage.getItem("orders")) || [];
  const userData = JSON.parse(localStorage.getItem("userData")) || {};
  const userEmail = userData.email;

  // Lọc đơn hàng của người dùng hiện tại
  let userOrders = userEmail
    ? orders.filter((order) => order.customer.email === userEmail)
    : [];

  // Lọc theo trạng thái
  if (statusFilter !== "all") {
    userOrders = userOrders.filter(
      (order) => order.status.toLowerCase() === statusFilter
    );
  }

  // Lọc theo thời gian
  if (dateFilter !== "all") {
    const now = new Date();
    let startDate;

    switch (dateFilter) {
      case "last-week":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "last-month":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "last-3-months":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case "last-6-months":
        startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = null;
    }

    if (startDate) {
      userOrders = userOrders.filter(
        (order) => new Date(order.orderDate) >= startDate
      );
    }
  }

  // Lọc theo từ khóa tìm kiếm
  if (searchQuery) {
    userOrders = userOrders.filter(
      (order) =>
        order.orderId.toLowerCase().includes(searchQuery) ||
        order.items.some((item) =>
          item.name.toLowerCase().includes(searchQuery)
        )
    );
  }

  // Hiển thị kết quả lọc
  displayOrders(userOrders);
}

/**
 * Khởi tạo phân trang
 */
function initPagination() {
  const prevButton = document.querySelector(".pagination-prev");
  const nextButton = document.querySelector(".pagination-next");
  const pageNumbers = document.querySelector(".pagination-numbers");

  // Thêm sự kiện click cho nút trang trước
  prevButton.addEventListener("click", function () {
    if (!this.hasAttribute("disabled")) {
      goToPreviousPage();
    }
  });

  // Thêm sự kiện click cho nút trang sau
  nextButton.addEventListener("click", function () {
    if (!this.hasAttribute("disabled")) {
      goToNextPage();
    }
  });

  // Thêm sự kiện click cho các nút số trang
  const pageButtons = pageNumbers.querySelectorAll("button");
  pageButtons.forEach((button) => {
    button.addEventListener("click", function () {
      goToPage(parseInt(this.textContent));
    });
  });
}

/**
 * Chuyển đến trang trước
 */
function goToPreviousPage() {
  const activeButton = document.querySelector(
    ".pagination-numbers button.active"
  );
  const currentPage = parseInt(activeButton.textContent);

  if (currentPage > 1) {
    goToPage(currentPage - 1);
  }
}

/**
 * Chuyển đến trang sau
 */
function goToNextPage() {
  const activeButton = document.querySelector(
    ".pagination-numbers button.active"
  );
  const currentPage = parseInt(activeButton.textContent);
  const totalPages = document.querySelectorAll(
    ".pagination-numbers button"
  ).length;

  if (currentPage < totalPages) {
    goToPage(currentPage + 1);
  }
}

/**
 * Chuyển đến trang cụ thể
 * @param {number} pageNumber - Số trang
 */
function goToPage(pageNumber) {
  // Cập nhật trạng thái active cho nút trang
  const pageButtons = document.querySelectorAll(".pagination-numbers button");
  pageButtons.forEach((button) => {
    if (parseInt(button.textContent) === pageNumber) {
      button.classList.add("active");
    } else {
      button.classList.remove("active");
    }
  });

  // Cập nhật trạng thái disabled cho nút trang trước/sau
  const prevButton = document.querySelector(".pagination-prev");
  const nextButton = document.querySelector(".pagination-next");

  prevButton.disabled = pageNumber === 1;
  nextButton.disabled = pageNumber === pageButtons.length;

  // Tải dữ liệu cho trang hiện tại
  // Trong ví dụ này, chúng ta sẽ giả lập việc phân trang
  // Trong thực tế, bạn sẽ cần tải dữ liệu từ API hoặc lọc dữ liệu từ localStorage
  loadOrders();
}

/**
 * Khởi tạo modal chi tiết đơn hàng
 */
function initOrderDetailModal() {
  const modal = document.getElementById("order-detail-modal");
  const closeButton = modal.querySelector(".modal-close");

  // Thêm sự kiện click cho nút đóng
  closeButton.addEventListener("click", function () {
    closeModal(modal);
  });

  // Thêm sự kiện click bên ngoài modal để đóng
  window.addEventListener("click", function (event) {
    if (event.target === modal) {
      closeModal(modal);
    }
  });

  // Thêm sự kiện click cho nút tải hóa đơn
  const downloadInvoiceBtn = document.getElementById("download-invoice-btn");
  downloadInvoiceBtn.addEventListener("click", function () {
    const orderId = document.getElementById("detail-order-id").textContent;
    downloadInvoice(orderId);
  });

  // Thêm sự kiện click cho nút theo dõi đơn hàng
  const trackOrderBtn = document.getElementById("track-order-btn");
  trackOrderBtn.addEventListener("click", function () {
    const orderId = document.getElementById("detail-order-id").textContent;
    trackOrder(orderId);
  });

  // Thêm sự kiện click cho nút hủy đơn hàng
  const cancelOrderBtn = document.getElementById("cancel-order-btn");
  cancelOrderBtn.addEventListener("click", function () {
    const orderId = document.getElementById("detail-order-id").textContent;
    closeModal(modal);
    openCancelOrderModal(orderId);
  });
}

/**
 * Mở modal chi tiết đơn hàng
 * @param {string} orderId - Mã đơn hàng
 */
function openOrderDetail(orderId) {
  const modal = document.getElementById("order-detail-modal");
  const orders = JSON.parse(localStorage.getItem("orders")) || [];
  const order = orders.find((o) => o.orderId === orderId);

  if (!order) {
    showNotification("Không tìm thấy thông tin đơn hàng", "error");
    return;
  }

  // Cập nhật thông tin đơn hàng trong modal
  document.getElementById("modal-order-id").textContent = order.orderId;
  document.getElementById("detail-order-id").textContent = order.orderId;
  document.getElementById("detail-order-date").textContent = formatDate(
    order.orderDate
  );
  document.getElementById("detail-order-status").textContent = getStatusText(
    order.status
  );
  document.getElementById(
    "detail-order-status"
  ).className = `order-status status-${order.status.toLowerCase()}`;
  document.getElementById("detail-payment-method").textContent =
    getPaymentMethodText(order.paymentMethod);

  // Cập nhật địa chỉ giao hàng
  const shippingAddress = document.getElementById("detail-shipping-address");
  shippingAddress.innerHTML = `
        <p><strong>${order.shippingAddress.fullName}</strong></p>
        <p>${order.shippingAddress.phone}</p>
        <p>${order.shippingAddress.address}</p>
        <p>${order.shippingAddress.ward}, ${order.shippingAddress.district}</p>
        <p>${order.shippingAddress.province}</p>
    `;

  // Cập nhật timeline
  updateOrderTimeline(order);

  // Cập nhật danh sách sản phẩm
  const orderItemsContainer = document.getElementById("detail-order-items");
  orderItemsContainer.innerHTML = "";

  order.items.forEach((item) => {
    const itemElement = document.createElement("div");
    itemElement.className = "order-item";
    itemElement.innerHTML = `
            <div class="item-col item-product">
                <div class="item-image">
                    <img src="${
                      item.image || "images/product-placeholder.jpg"
                    }" alt="${item.name}">
                </div>
                <div class="item-details">
                    <div class="item-name">${item.name}</div>
                    ${
                      item.variant
                        ? `<div class="item-variant">${item.variant}</div>`
                        : ""
                    }
                </div>
            </div>
            <div class="item-col item-price">${formatCurrency(item.price)}</div>
            <div class="item-col item-quantity">${item.quantity}</div>
            <div class="item-col item-total">${formatCurrency(
              item.price * item.quantity
            )}</div>
        `;
    orderItemsContainer.appendChild(itemElement);
  });

  // Cập nhật tổng cộng
  document.getElementById("detail-subtotal").textContent = formatCurrency(
    order.subtotal
  );
  document.getElementById("detail-shipping").textContent = formatCurrency(
    order.shipping
  );
  document.getElementById("detail-discount").textContent = formatCurrency(
    order.discount || 0
  );
  document.getElementById("detail-total").textContent = formatCurrency(
    order.total
  );

  // Hiển thị/ẩn nút hủy đơn hàng dựa trên trạng thái
  const cancelOrderBtn = document.getElementById("cancel-order-btn");
  if (order.status === "pending" || order.status === "processing") {
    cancelOrderBtn.style.display = "block";
  } else {
    cancelOrderBtn.style.display = "none";
  }

  // Hiển thị modal
  modal.style.display = "block";
  document.body.style.overflow = "hidden"; // Ngăn cuộn trang khi modal hiển thị
}

/**
 * Cập nhật timeline đơn hàng
 * @param {Object} order - Thông tin đơn hàng
 */
function updateOrderTimeline(order) {
  const timelineContainer = document.getElementById("order-timeline");
  timelineContainer.innerHTML = "";

  // Định nghĩa các trạng thái và mô tả
  const statuses = [
    {
      status: "pending",
      title: "Chờ xác nhận",
      description: "Đơn hàng của bạn đang chờ xác nhận",
    },
    {
      status: "processing",
      title: "Đang xử lý",
      description: "Đơn hàng của bạn đang được xử lý",
    },
    {
      status: "shipping",
      title: "Đang giao hàng",
      description: "Đơn hàng của bạn đang được giao",
    },
    {
      status: "completed",
      title: "Đã giao hàng",
      description: "Đơn hàng của bạn đã được giao thành công",
    },
  ];

  // Nếu đơn hàng đã hủy, chỉ hiển thị trạng thái hủy
  if (order.status === "cancelled") {
    const cancelledItem = document.createElement("div");
    cancelledItem.className = "timeline-item cancelled";
    cancelledItem.innerHTML = `
            <div class="timeline-content">
                <div class="timeline-date">${formatDate(
                  order.cancelledDate || order.orderDate
                )}</div>
                <div class="timeline-title">Đã hủy</div>
                <div class="timeline-description">${
                  order.cancelReason || "Đơn hàng đã bị hủy"
                }</div>
            </div>
        `;
    timelineContainer.appendChild(cancelledItem);
    return;
  }

  // Tạo timeline cho các trạng thái
  let activeFound = false;

  statuses.forEach((status) => {
    const isActive = order.status === status.status;
    const isPassed = !activeFound && !isActive;

    if (isActive) {
      activeFound = true;
    }

    const timelineItem = document.createElement("div");
    timelineItem.className = `timeline-item ${isActive ? "active" : ""} ${
      status.status
    }`;

    let timelineDate = "";
    switch (status.status) {
      case "pending":
        timelineDate = formatDate(order.orderDate);
        break;
      case "processing":
        timelineDate =
          isPassed || isActive
            ? formatDate(order.processingDate || order.orderDate)
            : "";
        break;
      case "shipping":
        timelineDate =
          isPassed || isActive ? formatDate(order.shippingDate || "") : "";
        break;
      case "completed":
        timelineDate =
          isPassed || isActive ? formatDate(order.completedDate || "") : "";
        break;
    }

    timelineItem.innerHTML = `
            <div class="timeline-content">
                <div class="timeline-date">${timelineDate}</div>
                <div class="timeline-title">${status.title}</div>
                <div class="timeline-description">${status.description}</div>
            </div>
        `;

    timelineContainer.appendChild(timelineItem);
  });
}

/**
 * Khởi tạo modal hủy đơn hàng
 */
function initCancelOrderModal() {
  const modal = document.getElementById("cancel-order-modal");
  const closeButton = modal.querySelector(".modal-close");
  const closeBtn = document.getElementById("cancel-order-close");
  const confirmBtn = document.getElementById("confirm-cancel-order");
  const cancelReasonSelect = document.getElementById("cancel-reason");
  const otherReasonGroup = document.getElementById("other-reason-group");

  // Thêm sự kiện click cho nút đóng
  closeButton.addEventListener("click", function () {
    closeModal(modal);
  });

  closeBtn.addEventListener("click", function () {
    closeModal(modal);
  });

  // Thêm sự kiện click bên ngoài modal để đóng
  window.addEventListener("click", function (event) {
    if (event.target === modal) {
      closeModal(modal);
    }
  });

  // Thêm sự kiện thay đổi cho select lý do hủy
  cancelReasonSelect.addEventListener("change", function () {
    if (this.value === "other") {
      otherReasonGroup.style.display = "block";
    } else {
      otherReasonGroup.style.display = "none";
    }
  });

  // Thêm sự kiện click cho nút xác nhận hủy
  confirmBtn.addEventListener("click", function () {
    const orderId = document.getElementById("cancel-order-id").textContent;
    const reasonSelect = document.getElementById("cancel-reason");
    const otherReasonInput = document.getElementById("other-reason");

    let cancelReason = "";

    if (reasonSelect.value === "") {
      showNotification("Vui lòng chọn lý do hủy đơn hàng", "error");
      return;
    } else if (reasonSelect.value === "other") {
      cancelReason = otherReasonInput.value.trim();
      if (cancelReason === "") {
        showNotification("Vui lòng nhập lý do hủy đơn hàng", "error");
        return;
      }
    } else {
      cancelReason = reasonSelect.options[reasonSelect.selectedIndex].text;
    }

    cancelOrder(orderId, cancelReason);
    closeModal(modal);
  });
}

/**
 * Mở modal hủy đơn hàng
 * @param {string} orderId - Mã đơn hàng
 */
function openCancelOrderModal(orderId) {
  const modal = document.getElementById("cancel-order-modal");
  document.getElementById("cancel-order-id").textContent = orderId;
  document.getElementById("cancel-reason").value = "";
  document.getElementById("other-reason").value = "";
  document.getElementById("other-reason-group").style.display = "none";

  // Hiển thị modal
  modal.style.display = "block";
  document.body.style.overflow = "hidden"; // Ngăn cuộn trang khi modal hiển thị
}

/**
 * Hủy đơn hàng
 * @param {string} orderId - Mã đơn hàng
 * @param {string} reason - Lý do hủy
 */
function cancelOrder(orderId, reason) {
  // Lấy dữ liệu đơn hàng từ localStorage
  const orders = JSON.parse(localStorage.getItem("orders")) || [];
  const orderIndex = orders.findIndex((o) => o.orderId === orderId);

  if (orderIndex === -1) {
    showNotification("Không tìm thấy thông tin đơn hàng", "error");
    return;
  }

  // Cập nhật trạng thái đơn hàng
  orders[orderIndex].status = "cancelled";
  orders[orderIndex].cancelReason = reason;
  orders[orderIndex].cancelledDate = new Date().toISOString();

  // Lưu lại vào localStorage
  localStorage.setItem("orders", JSON.stringify(orders));

  // Hiển thị thông báo
  showNotification("Đơn hàng đã được hủy thành công", "success");

  // Tải lại danh sách đơn hàng
  loadOrders();
}

/**
 * Tải hóa đơn
 * @param {string} orderId - Mã đơn hàng
 */
function downloadInvoice(orderId) {
  // Trong thực tế, bạn sẽ gọi API để tải hóa đơn
  // Ở đây, chúng ta sẽ giả lập việc tải hóa đơn
  showNotification("Đang tải hóa đơn...", "info");

  setTimeout(() => {
    showNotification("Hóa đơn đã được tải xuống thành công", "success");
  }, 1500);
}

/**
 * Theo dõi đơn hàng
 * @param {string} orderId - Mã đơn hàng
 */
function trackOrder(orderId) {
  // Trong thực tế, bạn sẽ chuyển hướng đến trang theo dõi đơn hàng
  // Ở đây, chúng ta sẽ mở modal chi tiết đơn hàng
  openOrderDetail(orderId);
}

/**
 * Đóng modal
 * @param {HTMLElement} modal - Phần tử modal
 */
function closeModal(modal) {
  modal.style.display = "none";
  document.body.style.overflow = ""; // Khôi phục cuộn trang
}

/**
 * Khởi tạo nút đăng xuất
 */
function initLogout() {
  const logoutButton = document.getElementById("logout-button");
  const sidebarLogout = document.getElementById("sidebar-logout");

  logoutButton.addEventListener("click", logout);
  sidebarLogout.addEventListener("click", logout);
}

/**
 * Đăng xuất
 */
function logout() {
  // Xóa thông tin đăng nhập
  localStorage.removeItem("isLoggedIn");
  localStorage.removeItem("userData");

  // Chuyển hướng đến trang đăng nhập
  window.location.href = "login.html";
}

/**
 * Định dạng ngày tháng
 * @param {string} dateString - Chuỗi ngày tháng
 * @returns {string} - Chuỗi ngày tháng đã định dạng
 */
function formatDate(dateString) {
  if (!dateString) return "";

  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");

  return `${day}/${month}/${year} ${hours}:${minutes}`;
}

/**
 * Định dạng tiền tệ
 * @param {number} amount - Số tiền
 * @returns {string} - Chuỗi tiền tệ đã định dạng
 */
function formatCurrency(amount) {
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
  }).format(amount);
}

/**
 * Lấy văn bản trạng thái đơn hàng
 * @param {string} status - Trạng thái đơn hàng
 * @returns {string} - Văn bản trạng thái
 */
function getStatusText(status) {
  switch (status.toLowerCase()) {
    case "pending":
      return "Chờ xác nhận";
    case "processing":
      return "Đang xử lý";
    case "shipping":
      return "Đang giao hàng";
    case "completed":
      return "Đã giao hàng";
    case "cancelled":
      return "Đã hủy";
    default:
      return status;
  }
}

/**
 * Lấy văn bản phương thức thanh toán
 * @param {string} method - Phương thức thanh toán
 * @returns {string} - Văn bản phương thức thanh toán
 */
function getPaymentMethodText(method) {
  switch (method) {
    case "cod":
      return "Thanh toán khi nhận hàng (COD)";
    case "bank_transfer":
      return "Chuyển khoản ngân hàng";
    case "credit_card":
      return "Thẻ tín dụng/Ghi nợ";
    case "momo":
      return "Ví MoMo";
    case "zalopay":
      return "ZaloPay";
    default:
      return method;
  }
}

/**
 * Hiển thị thông báo
 * @param {string} message - Nội dung thông báo
 * @param {string} type - Loại thông báo (success, error, info, warning)
 */
function showNotification(message, type = "info") {
  // Kiểm tra xem đã có container thông báo chưa
  let notificationContainer = document.querySelector(".notification-container");

  // Nếu chưa có, tạo mới
  if (!notificationContainer) {
    notificationContainer = document.createElement("div");
    notificationContainer.className = "notification-container";
    document.body.appendChild(notificationContainer);

    // Thêm CSS cho container
    notificationContainer.style.position = "fixed";
    notificationContainer.style.top = "20px";
    notificationContainer.style.right = "20px";
    notificationContainer.style.zIndex = "9999";
  }

  // Tạo thông báo
  const notification = document.createElement("div");
  notification.className = `notification notification-${type}`;

  // Thêm CSS cho thông báo
  notification.style.backgroundColor = "#fff";
  notification.style.borderRadius = "4px";
  notification.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.1)";
  notification.style.padding = "15px 20px";
  notification.style.marginBottom = "10px";
  notification.style.display = "flex";
  notification.style.alignItems = "center";
  notification.style.transition = "all 0.3s ease";
  notification.style.position = "relative";
  notification.style.borderLeft = "4px solid #ccc";

  // Đặt màu viền trái theo loại thông báo
  switch (type) {
    case "success":
      notification.style.borderLeftColor = "#4CAF50";
      break;
    case "error":
      notification.style.borderLeftColor = "#f44336";
      break;
    case "warning":
      notification.style.borderLeftColor = "#ff9800";
      break;
    case "info":
      notification.style.borderLeftColor = "#2196F3";
      break;
  }

  // Tạo icon theo loại thông báo
  let icon = "";
  switch (type) {
    case "success":
      icon =
        '<i class="fas fa-check-circle" style="color: #4CAF50; margin-right: 10px; font-size: 18px;"></i>';
      break;
    case "error":
      icon =
        '<i class="fas fa-times-circle" style="color: #f44336; margin-right: 10px; font-size: 18px;"></i>';
      break;
    case "warning":
      icon =
        '<i class="fas fa-exclamation-triangle" style="color: #ff9800; margin-right: 10px; font-size: 18px;"></i>';
      break;
    case "info":
      icon =
        '<i class="fas fa-info-circle" style="color: #2196F3; margin-right: 10px; font-size: 18px;"></i>';
      break;
  }

  // Tạo nút đóng
  const closeButton =
    '<button class="notification-close" style="background: none; border: none; color: #999; cursor: pointer; position: absolute; top: 10px; right: 10px; font-size: 14px;"><i class="fas fa-times"></i></button>';

  // Thêm nội dung vào thông báo
  notification.innerHTML = `${icon}<div>${message}</div>${closeButton}`;

  // Thêm thông báo vào container
  notificationContainer.appendChild(notification);

  // Thêm sự kiện click cho nút đóng
  notification
    .querySelector(".notification-close")
    .addEventListener("click", function () {
      notification.remove();
    });

  // Tự động đóng thông báo sau 5 giây
  setTimeout(() => {
    notification.style.opacity = "0";
    notification.style.transform = "translateX(20px)";

    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 5000);
}
