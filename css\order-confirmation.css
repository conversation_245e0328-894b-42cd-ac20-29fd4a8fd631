/* Order Confirmation Page Styles */

/* Confirmation Section */
.confirmation-section {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.confirmation-container {
  max-width: 900px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* Confirmation Header */
.confirmation-header {
  text-align: center;
  padding: 40px 30px;
  background-color: #f1f8e9;
  border-bottom: 1px solid #e0e0e0;
}

.success-icon {
  font-size: 60px;
  color: #4caf50;
  margin-bottom: 20px;
}

.confirmation-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}

.confirmation-header p {
  font-size: 16px;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Order Info */
.order-info {
  padding: 30px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.order-number {
  flex: 1;
}

.order-number h2 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.order-number p {
  font-size: 15px;
  color: #666;
}

.order-status {
  flex: 2;
}

.order-status h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

/* Status Timeline */
.status-timeline {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-top: 30px;
}

.status-timeline::before {
  content: "";
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

.status-step {
  position: relative;
  z-index: 2;
  text-align: center;
  width: 80px;
}

.step-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  color: #fff;
  font-size: 14px;
  transition: all 0.3s ease;
}

.step-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.status-step.active .step-icon {
  background-color: #4caf50;
}

.status-step.active .step-label {
  color: #4caf50;
  font-weight: 600;
}

/* Order Details */
.order-details {
  padding: 30px;
  border-bottom: 1px solid #e0e0e0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.details-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.details-content p {
  margin-bottom: 8px;
  font-size: 15px;
  color: #555;
  line-height: 1.5;
}

.details-content strong {
  font-weight: 600;
  color: #333;
}

/* Order Summary */
.order-summary {
  padding: 30px;
  border-bottom: 1px solid #e0e0e0;
}

.order-summary h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.order-items {
  margin-bottom: 30px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

.item-header .item-name {
  flex: 2;
}

.item-header .item-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.item-image {
  width: 80px;
  height: 80px;
  margin-right: 15px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  flex: 2;
}

.item-content .item-name {
  font-weight: 500;
  margin-bottom: 5px;
  color: #333;
}

.item-content .item-variant {
  font-size: 14px;
  color: #666;
}

.item-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.item-price,
.item-quantity,
.item-total {
  flex: 1;
  text-align: right;
}

.item-total {
  font-weight: 600;
  color: #333;
}

.order-totals {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 4px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 15px;
  color: #555;
}

.grand-total {
  font-weight: 700;
  color: #333;
  font-size: 18px;
  padding-top: 10px;
  margin-top: 10px;
  border-top: 1px solid #e0e0e0;
}

/* Confirmation Actions */
.confirmation-actions {
  padding: 30px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.confirmation-actions .btn {
  min-width: 200px;
  text-align: center;
}

/* Confirmation Help */
.confirmation-help {
  padding: 30px;
  text-align: center;
  background-color: #f5f5f5;
}

.confirmation-help h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.confirmation-help p {
  font-size: 15px;
  color: #555;
  margin-bottom: 20px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.help-contacts {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.help-contact {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.help-contact i {
  color: #4caf50;
  font-size: 18px;
}

/* Recommended Products */
.recommended-products {
  padding: 60px 0;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 40px;
  color: #333;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: #4caf50;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .order-info {
    flex-direction: column;
    gap: 20px;
  }

  .status-timeline {
    margin-top: 20px;
  }

  .order-details {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .confirmation-actions {
    flex-direction: column;
  }

  .confirmation-actions .btn {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .confirmation-header {
    padding: 30px 20px;
  }

  .confirmation-header h1 {
    font-size: 24px;
  }

  .order-info,
  .order-details,
  .order-summary,
  .confirmation-actions,
  .confirmation-help {
    padding: 20px;
  }

  .item-details {
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
  }

  .item-header .item-details {
    display: none;
  }

  .status-step {
    width: 60px;
  }

  .step-label {
    font-size: 11px;
  }
}

@media (max-width: 576px) {
  .confirmation-header {
    padding: 25px 15px;
  }

  .success-icon {
    font-size: 50px;
  }

  .confirmation-header h1 {
    font-size: 22px;
  }

  .order-info,
  .order-details,
  .order-summary,
  .confirmation-actions,
  .confirmation-help {
    padding: 15px;
  }

  .order-item {
    flex-wrap: wrap;
  }

  .item-content {
    flex: 1 0 calc(100% - 95px);
  }

  .item-details {
    flex: 1 0 100%;
    margin-top: 10px;
    padding-left: 95px;
  }

  .help-contacts {
    flex-direction: column;
    gap: 15px;
  }
}
