// Cart Page JavaScript

document.addEventListener("DOMContentLoaded", function () {
  // Initialize variables
  const cartItemsContainer = document.getElementById("cart-items-container");
  const cartItemTemplate = document.getElementById("cart-item-template");
  const emptyCartMessage = document.querySelector(".empty-cart-message");
  const totalItemsElement = document.getElementById("total-items");
  const subtotalElement = document.getElementById("subtotal");
  const discountElement = document.getElementById("discount");
  const totalElement = document.getElementById("total");
  const couponCodeInput = document.getElementById("coupon-code");
  const applyCouponButton = document.getElementById("apply-coupon");
  const couponMessageElement = document.getElementById("coupon-message");
  const checkoutButton = document.querySelector(".checkout-btn");

  // Available coupon codes
  const availableCoupons = {
    WELCOME10: {
      discount: 0.1,
      description: "Giảm 10% tổng đơn hàng",
    },
    ELECTRO20: {
      discount: 0.2,
      description: "Giảm 20% tổng đơn hàng",
    },
    FREESHIP: {
      discount: 0,
      freeShipping: true,
      description: "Miễn phí vận chuyển",
    },
  };

  // Cart state
  let cartState = {
    items: [],
    subtotal: 0,
    discount: 0,
    total: 0,
    appliedCoupon: null,
  };

  // Load cart from localStorage
  function loadCart() {
    const savedCart = JSON.parse(localStorage.getItem("cart")) || [];
    cartState.items = savedCart;
    renderCart();
    updateCartSummary();
  }

  // Save cart to localStorage
  function saveCart() {
    localStorage.setItem("cart", JSON.stringify(cartState.items));
    updateCartCount();
  }

  // Render cart items
  function renderCart() {
    // Clear container
    while (cartItemsContainer.firstChild) {
      cartItemsContainer.removeChild(cartItemsContainer.firstChild);
    }

    // Show empty cart message if no items
    if (cartState.items.length === 0) {
      emptyCartMessage.style.display = "block";
      return;
    }

    emptyCartMessage.style.display = "none";

    // Render each cart item
    cartState.items.forEach((item, index) => {
      const cartItemElement = document.importNode(
        cartItemTemplate.content,
        true
      );
      const cartItem = cartItemElement.querySelector(".cart-item");

      // Set data attributes
      cartItem.setAttribute("data-id", index);

      // Set product details
      const productTitle = cartItem.querySelector(".product-title");
      const productImage = cartItem.querySelector(".product-image img");
      const productColor = cartItem.querySelector(".product-color");
      const productPrice = cartItem.querySelector(".product-price");
      const quantityInput = cartItem.querySelector(".quantity-input");
      const productTotal = cartItem.querySelector(".product-total");

      productTitle.textContent = item.title;
      // In a real application, you would set the actual product image
      productImage.src = `images/car${(index % 12) + 1}.jpg`;
      productImage.alt = item.title;
      productColor.textContent = item.color;
      productPrice.textContent = formatCurrency(item.price);
      quantityInput.value = item.quantity;
      productTotal.textContent = formatCurrency(item.price * item.quantity);

      // Add data labels for responsive design
      cartItem
        .querySelector(".price-col")
        .setAttribute("data-label", "Đơn giá:");
      cartItem
        .querySelector(".quantity-col")
        .setAttribute("data-label", "Số lượng:");
      cartItem
        .querySelector(".total-col")
        .setAttribute("data-label", "Thành tiền:");

      // Add event listeners
      const minusBtn = cartItem.querySelector(".quantity-btn.minus");
      const plusBtn = cartItem.querySelector(".quantity-btn.plus");
      const removeBtn = cartItem.querySelector(".remove-item");

      minusBtn.addEventListener("click", () => updateQuantity(index, -1));
      plusBtn.addEventListener("click", () => updateQuantity(index, 1));
      removeBtn.addEventListener("click", () => removeItem(index));
      quantityInput.addEventListener("change", (e) =>
        setQuantity(index, parseInt(e.target.value))
      );

      cartItemsContainer.appendChild(cartItemElement);
    });
  }

  // Update item quantity
  function updateQuantity(index, change) {
    const item = cartState.items[index];
    const newQuantity = item.quantity + change;

    if (newQuantity < 1) return;
    if (newQuantity > 10) {
      showNotification("Số lượng tối đa là 10", "error");
      return;
    }

    item.quantity = newQuantity;
    saveCart();
    renderCart();
    updateCartSummary();
  }

  // Set specific quantity
  function setQuantity(index, quantity) {
    const item = cartState.items[index];

    if (isNaN(quantity) || quantity < 1) {
      quantity = 1;
    } else if (quantity > 10) {
      quantity = 10;
      showNotification("Số lượng tối đa là 10", "error");
    }

    item.quantity = quantity;
    saveCart();
    renderCart();
    updateCartSummary();
  }

  // Remove item from cart
  function removeItem(index) {
    const item = cartState.items[index];
    const confirmRemove = confirm(
      `Bạn có chắc chắn muốn xóa ${item.title} khỏi giỏ hàng?`
    );

    if (confirmRemove) {
      cartState.items.splice(index, 1);
      saveCart();
      renderCart();
      updateCartSummary();
      showNotification("Đã xóa sản phẩm khỏi giỏ hàng");
    }
  }

  // Update cart summary
  function updateCartSummary() {
    // Calculate subtotal
    cartState.subtotal = cartState.items.reduce((total, item) => {
      return total + item.price * item.quantity;
    }, 0);

    // Apply discount if coupon is applied
    if (cartState.appliedCoupon) {
      const coupon = availableCoupons[cartState.appliedCoupon];
      if (coupon) {
        cartState.discount = cartState.subtotal * coupon.discount;
      }
    } else {
      cartState.discount = 0;
    }

    // Calculate total
    cartState.total = cartState.subtotal - cartState.discount;

    // Update DOM
    const totalItems = cartState.items.reduce(
      (total, item) => total + item.quantity,
      0
    );
    totalItemsElement.textContent = totalItems;
    subtotalElement.textContent = formatCurrency(cartState.subtotal);
    discountElement.textContent = formatCurrency(cartState.discount);
    totalElement.textContent = formatCurrency(cartState.total);

    // Update checkout button state
    checkoutButton.disabled = cartState.items.length === 0;
    if (cartState.items.length === 0) {
      checkoutButton.classList.add("disabled");
    } else {
      checkoutButton.classList.remove("disabled");
    }
  }

  // Apply coupon code
  function applyCoupon() {
    const couponCode = couponCodeInput.value.trim().toUpperCase();

    if (couponCode === "") {
      showCouponMessage("Vui lòng nhập mã giảm giá", "error");
      return;
    }

    const coupon = availableCoupons[couponCode];
    if (!coupon) {
      showCouponMessage("Mã giảm giá không hợp lệ", "error");
      return;
    }

    cartState.appliedCoupon = couponCode;
    updateCartSummary();

    let message = coupon.description;
    if (coupon.discount > 0) {
      const discountAmount = formatCurrency(cartState.discount);
      message += ` (Tiết kiệm ${discountAmount})`;
    }

    showCouponMessage(message, "success");
    showNotification("Đã áp dụng mã giảm giá thành công", "success");
  }

  // Show coupon message
  function showCouponMessage(message, type) {
    couponMessageElement.textContent = message;
    couponMessageElement.className = type;
  }

  // Update cart count in header
  function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem("cart")) || [];
    const cartCount = cart.reduce((total, item) => total + item.quantity, 0);

    // Update cart count in header
    const cartCountElement = document.querySelector(".cart-count");
    if (cartCountElement) {
      cartCountElement.textContent = cartCount;
    }
  }

  // Format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
      maximumFractionDigits: 0,
    }).format(amount);
  }

  // Show notification
  function showNotification(message, type = "info") {
    // Check if notification container exists, if not create it
    let notificationContainer = document.querySelector(
      ".notification-container"
    );
    if (!notificationContainer) {
      notificationContainer = document.createElement("div");
      notificationContainer.className = "notification-container";
      document.body.appendChild(notificationContainer);
    }

    // Create notification element
    const notification = document.createElement("div");
    notification.className = `notification ${type}`;
    notification.innerHTML = message;

    // Add to container
    notificationContainer.appendChild(notification);

    // Remove after delay
    setTimeout(() => {
      notification.classList.add("hide");
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 3000);
  }

  // Event Listeners
  applyCouponButton.addEventListener("click", applyCoupon);

  // Initialize cart
  loadCart();
});

// Add CSS for notifications
const notificationStyles = document.createElement("style");
notificationStyles.textContent = `
    .notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
    }
    
    .notification {
        background-color: #fff;
        color: #333;
        padding: 15px 20px;
        margin-bottom: 10px;
        border-radius: 5px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        animation: slideIn 0.3s ease-out forwards;
        max-width: 300px;
    }
    
    .notification.info {
        border-left: 4px solid var(--primary-color);
    }
    
    .notification.success {
        border-left: 4px solid #4CAF50;
    }
    
    .notification.error {
        border-left: 4px solid #F44336;
    }
    
    .notification.hide {
        animation: slideOut 0.3s ease-out forwards;
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .btn.disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
`;

document.head.appendChild(notificationStyles);
