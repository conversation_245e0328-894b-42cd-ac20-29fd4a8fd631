/* Products Page Styles */

/* Page Title */
.page-title {
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
    url(/images/car2-side.jpg);
  background-size: cover;
  background-position: center;
  padding: 80px 0;
  text-align: center;
  color: white;
}

.page-title h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.breadcrumb {
  display: flex;
  justify-content: center;
  align-items: center;
}

.breadcrumb a {
  color: #4caf50;
}

.breadcrumb .separator {
  margin: 0 10px;
  color: #ddd;
}

.breadcrumb .current {
  color: #ddd;
}

/* Products Section */
.products-section {
  padding: 80px 0;
  background-color: #f9f9f9;
}

.products-wrapper {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 40px;
}

/* Sidebar Filters */
.product-sidebar {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  align-self: start;
  position: sticky;
  top: 100px;
}

.filter-box {
  margin-bottom: 30px;
}

.filter-box h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.category-filter,
.range-filter,
.charging-filter {
  list-style: none;
}

.category-filter li,
.range-filter li,
.charging-filter li {
  margin-bottom: 10px;
}

.category-filter label,
.range-filter label,
.charging-filter label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.category-filter input[type="checkbox"],
.range-filter input[type="checkbox"],
.charging-filter input[type="checkbox"] {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  accent-color: #4caf50;
}

/* Price Filter */
.price-filter {
  margin-top: 15px;
}

.price-inputs {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.price-inputs input {
  width: 100px;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 0.9rem;
}

.price-inputs span {
  margin: 0 10px;
  color: #777;
}

.price-slider {
  position: relative;
  height: 5px;
  background-color: #eee;
  border-radius: 5px;
  margin: 20px 0;
}

.slider-track {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #2196f3);
  border-radius: 5px;
  z-index: 1;
}

.price-slider input[type="range"] {
  position: absolute;
  width: 100%;
  height: 5px;
  background: none;
  pointer-events: none;
  -webkit-appearance: none;
  appearance: none;
  outline: none;
  z-index: 2;
}

.price-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background-color: white;
  border: 2px solid #4caf50;
  border-radius: 50%;
  cursor: pointer;
  pointer-events: auto;
}

.filter-apply-btn {
  width: 100%;
  margin-bottom: 10px;
}

.filter-reset-btn {
  width: 100%;
}

/* Products Container */
.products-container {
  display: flex;
  flex-direction: column;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.products-count {
  font-size: 0.95rem;
  color: #777;
}

.products-count span {
  font-weight: 700;
  color: #333;
}

.products-sort {
  display: flex;
  align-items: center;
}

.products-sort label {
  margin-right: 10px;
  font-size: 0.95rem;
  color: #777;
}

.products-sort select {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 0.95rem;
  outline: none;
  cursor: pointer;
}

/* Product Grid */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

/* Product Card (Additional Styles) */
.product-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-actions {
  opacity: 1;
}

.action-btn {
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 5px;
  color: #333;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background-color: #4caf50;
  color: white;
  transform: translateY(-3px);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 50px;
}

.pagination a {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  margin: 0 5px;
  border-radius: 5px;
  background-color: white;
  color: #333;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.pagination a.active {
  background-color: #4caf50;
  color: white;
}

.pagination a:hover:not(.active) {
  background-color: #f5f5f5;
}

.pagination a.next {
  width: auto;
  padding: 0 15px;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .products-wrapper {
    grid-template-columns: 250px 1fr;
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .products-wrapper {
    grid-template-columns: 1fr;
  }

  .product-sidebar {
    position: static;
    margin-bottom: 30px;
  }

  .products-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .products-sort {
    margin-top: 15px;
  }
}

@media (max-width: 576px) {
  .product-grid {
    grid-template-columns: 1fr;
  }

  .pagination a {
    width: 35px;
    height: 35px;
    margin: 0 3px;
  }
}
