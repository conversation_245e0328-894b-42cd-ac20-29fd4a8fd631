/* Auth Pages Styles */

/* Auth Section */
.auth-section {
  padding: 60px 0;
  min-height: calc(100vh - 80px - 350px);
  display: flex;
  align-items: center;
}

.auth-container {
  display: flex;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
}

.auth-content {
  flex: 1;
  padding: 40px;
}

.auth-image {
  flex: 1;
  position: relative;
  background: linear-gradient(rgba(60, 60, 60, 0.6), rgba(60, 60, 60, 0.6)), url(/images/car1-rear.jpg) center/cover no-repeat
    
}

.auth-image img {
  width: 100%;
  height: 46px;
  /* object-fit: cover; */
}

.auth-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 123, 255, 0.8),
    rgba(0, 183, 148, 0.9)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.overlay-content {
  color: #fff;
  text-align: center;
}

.overlay-content h2 {
  font-size: 28px;
  margin-bottom: 20px;
}

.overlay-content p {
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.6;
}

.steps-list {
  list-style: none;
  padding: 0;
  text-align: left;
}

.steps-list li {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-right: 15px;
  font-weight: bold;
}

/* Auth Header */
.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h1 {
  font-size: 28px;
  color: #333;
  margin-bottom: 10px;
}

.auth-header p {
  color: #666;
  font-size: 16px;
}

/* Auth Form */
.auth-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group .required {
  color: #e74c3c;
}

.input-icon-wrapper {
  position: relative;
}

.input-icon-wrapper i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #aaa;
  font-size: 16px;
}

.input-icon-wrapper input {
  padding-left: 35px;
}

.toggle-password {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #aaa;
  cursor: pointer;
}

.toggle-password:hover {
  color: #666;
}

.auth-form input[type="text"],
.auth-form input[type="email"],
.auth-form input[type="password"],
.auth-form input[type="tel"] {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.auth-form input:focus {
  border-color: #00b794;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 183, 148, 0.2);
}

.auth-form input::placeholder {
  color: #aaa;
}

/* Password Strength Meter */
.password-strength {
  margin-top: 10px;
}

.strength-meter {
  height: 5px;
  background-color: #eee;
  border-radius: 3px;
  margin-bottom: 5px;
}

.strength-meter-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s, background-color 0.3s;
}

.strength-meter-fill[data-strength="0"] {
  width: 20%;
  background-color: #e74c3c;
}

.strength-meter-fill[data-strength="1"] {
  width: 40%;
  background-color: #e67e22;
}

.strength-meter-fill[data-strength="2"] {
  width: 60%;
  background-color: #f1c40f;
}

.strength-meter-fill[data-strength="3"] {
  width: 80%;
  background-color: #2ecc71;
}

.strength-meter-fill[data-strength="4"] {
  width: 100%;
  background-color: #27ae60;
}

.strength-text {
  font-size: 12px;
  color: #666;
}

/* Checkbox Styles */
.checkbox-group {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 10px;
  margin-top: 3px;
}

.checkbox-group label {
  font-size: 14px;
  color: #666;
  margin-bottom: 0;
}

.checkbox-group a {
  color: #00b794;
  text-decoration: none;
}

.checkbox-group a:hover {
  text-decoration: underline;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.btn-primary {
  background-color: #00b794;
  color: #fff;
}

.btn-primary:hover {
  background-color: #009d7e;
}

.btn-secondary {
  background-color: #27ae60;
  color: #333;
  border: 1px solid #ddd;
}

.btn-secondary:hover {
  background-color: #e9ecef;
}

.btn-block {
  display: block;
  width: 100%;
}

/* Social Login */
.social-login {
  margin-top: 30px;
  text-align: center;
}

.social-login-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.social-login-title::before,
.social-login-title::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: #ddd;
}

.social-login-title span {
  padding: 0 15px;
  color: #666;
  font-size: 14px;
}

.social-buttons {
  display: flex;
  gap: 15px;
}

.social-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: 5px;
  color: #fff;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s;
}

.social-button i {
  margin-right: 10px;
}

.facebook-button {
  background-color: #3b5998;
}

.facebook-button:hover {
  background-color: #344e86;
}

.google-button {
  background-color: #dd4b39;
}

.google-button:hover {
  background-color: #c23321;
}

/* Auth Footer */
.auth-footer {
  text-align: center;
  margin-top: 30px;
  color: #666;
  font-size: 14px;
}

.auth-footer a {
  color: #00b794;
  text-decoration: none;
  font-weight: 500;
}

.auth-footer a:hover {
  text-decoration: underline;
}

/* Form Note */
.form-note {
  margin-top: 15px;
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* Verification Code Input */
.verification-code-container {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.verification-input {
  flex: 1;
  width: 50px;
  height: 50px;
  text-align: center;
  font-size: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.verification-note {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.countdown {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

/* Success Message */
.auth-success {
  text-align: center;
  padding: 30px 0;
}

.success-icon {
  font-size: 60px;
  color: #2ecc71;
  margin-bottom: 20px;
}

.auth-success h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 15px;
}

.auth-success p {
  color: #666;
  margin-bottom: 30px;
}

/* Responsive Styles */
@media (max-width: 991px) {
  .auth-container {
    flex-direction: column;
    max-width: 500px;
  }

  .auth-content {
    order: 2;
  }

  .auth-image {
    order: 1;
    height: 200px;
  }
}

@media (max-width: 576px) {
  .auth-section {
    padding: 30px 0;
  }

  .auth-content {
    padding: 30px 20px;
  }

  .social-buttons {
    flex-direction: column;
  }

  .verification-code-container {
    gap: 5px;
  }

  .verification-input {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}
