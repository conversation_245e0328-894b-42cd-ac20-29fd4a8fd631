document.addEventListener("DOMContentLoaded", function () {
  // Initialize order confirmation page
  initOrderConfirmation();
});

/**
 * Initialize order confirmation page
 */
function initOrderConfirmation() {
  // Get order ID from URL parameter
  const urlParams = new URLSearchParams(window.location.search);
  const orderId = urlParams.get("order_id");

  // If order ID is provided in URL, load that specific order
  if (orderId) {
    loadOrderDetails(orderId);
  } else {
    // Otherwise, load the most recent order
    loadMostRecentOrder();
  }

  // Initialize download invoice button
  document
    .getElementById("download-invoice")
    .addEventListener("click", function (e) {
      e.preventDefault();
      generateInvoice();
    });
}

/**
 * Load most recent order from localStorage
 */
function loadMostRecentOrder() {
  // Get orders from localStorage
  const orders = JSON.parse(localStorage.getItem("orders")) || [];

  // If no orders, redirect to home page
  if (orders.length === 0) {
    window.location.href = "index.html";
    return;
  }

  // Get most recent order
  const order = orders[orders.length - 1];

  // Display order details
  displayOrderDetails(order);
}

/**
 * Load specific order details by ID
 * @param {string} orderId - Order ID to load
 */
function loadOrderDetails(orderId) {
  // Get orders from localStorage
  const orders = JSON.parse(localStorage.getItem("orders")) || [];

  // Find order with matching ID
  const order = orders.find((order) => order.id === orderId);

  // If order not found, load most recent order
  if (!order) {
    loadMostRecentOrder();
    return;
  }

  // Display order details
  displayOrderDetails(order);
}

/**
 * Display order details on the page
 * @param {Object} order - Order object to display
 */
function displayOrderDetails(order) {
  // Set order ID and date
  document.getElementById("order-id").textContent = order.id;
  document.getElementById("order-date").textContent = formatDate(
    new Date(order.date)
  );

  // Set customer information
  document.getElementById("customer-name").textContent =
    order.customer.fullname;
  document.getElementById("customer-email").textContent = order.customer.email;
  document.getElementById("customer-phone").textContent = order.customer.phone;

  // Set shipping address
  const shippingAddress = `${order.shipping.address}, ${order.shipping.ward}, ${order.shipping.district}, ${order.shipping.city}`;
  document.getElementById("shipping-address").textContent = shippingAddress;

  // Set payment method
  let paymentMethodText = "Không xác định";
  switch (order.payment.method) {
    case "cod":
      paymentMethodText = "Thanh toán khi nhận hàng (COD)";
      break;
    case "bank":
      paymentMethodText = "Chuyển khoản ngân hàng";
      break;
    case "credit":
      paymentMethodText = "Thẻ tín dụng/Thẻ ghi nợ";
      break;
    case "momo":
      paymentMethodText = "Ví MoMo";
      break;
  }
  document.getElementById("payment-method").textContent = paymentMethodText;

  // Display order items
  displayOrderItems(order.order.items);

  // Set order totals
  document.getElementById("order-subtotal").textContent = formatCurrency(
    order.order.subtotal
  );
  document.getElementById("order-shipping").textContent = formatCurrency(
    order.order.shipping
  );
  document.getElementById("order-discount").textContent =
    "-" + formatCurrency(order.order.discount);
  document.getElementById("order-total").textContent = formatCurrency(
    order.order.total
  );
}

/**
 * Display order items in the order summary
 * @param {Array} items - Array of order items
 */
function displayOrderItems(items) {
  const container = document.getElementById("order-items-container");
  const template = document.getElementById("order-item-template");

  // Clear container
  container.innerHTML = "";

  // Add each item to the container
  items.forEach((item) => {
    // Clone template
    const orderItem = document.importNode(template.content, true);

    // Set item image
    const imgElement = orderItem.querySelector(".item-image img");
    imgElement.src = item.image || "images/product-placeholder.jpg";
    imgElement.alt = item.name;

    // Set item details
    orderItem.querySelector(".item-name").textContent = item.name;
    orderItem.querySelector(".item-color").textContent =
      item.color || "Mặc định";
    orderItem.querySelector(".item-price").textContent = formatCurrency(
      item.price
    );
    orderItem.querySelector(".quantity-value").textContent = item.quantity;
    orderItem.querySelector(".item-total").textContent = formatCurrency(
      item.price * item.quantity
    );

    // Add to container
    container.appendChild(orderItem);
  });
}

/**
 * Generate and download invoice
 */
function generateInvoice() {
  // In a real application, this would generate a PDF invoice
  // For this demo, we'll just show a notification
  showNotification(
    "Tính năng tải hóa đơn sẽ được cập nhật trong thời gian tới!",
    "info"
  );
}

/**
 * Format date to DD/MM/YYYY
 * @param {Date} date - Date to format
 * @returns {string} Formatted date string
 */
function formatDate(date) {
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
}

/**
 * Format currency
 * @param {number} amount - Amount to format
 * @returns {string} Formatted currency string
 */
function formatCurrency(amount) {
  return new Intl.NumberFormat("vi-VN", { style: "currency", currency: "VND" })
    .format(amount)
    .replace("₫", "₫");
}

/**
 * Show notification
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, error, info)
 */
function showNotification(message, type = "info") {
  // Create notification element if it doesn't exist
  let notification = document.querySelector(".notification");

  if (!notification) {
    notification = document.createElement("div");
    notification.className = "notification";
    document.body.appendChild(notification);

    // Add styles
    notification.style.position = "fixed";
    notification.style.top = "20px";
    notification.style.right = "20px";
    notification.style.padding = "15px 20px";
    notification.style.borderRadius = "4px";
    notification.style.color = "#fff";
    notification.style.fontWeight = "500";
    notification.style.zIndex = "9999";
    notification.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
    notification.style.transition = "all 0.3s ease";
    notification.style.opacity = "0";
    notification.style.transform = "translateY(-20px)";
  }

  // Set notification type
  if (type === "success") {
    notification.style.backgroundColor = "#4CAF50";
  } else if (type === "error") {
    notification.style.backgroundColor = "#F44336";
  } else {
    notification.style.backgroundColor = "#2196F3";
  }

  // Set message
  notification.textContent = message;

  // Show notification
  setTimeout(() => {
    notification.style.opacity = "1";
    notification.style.transform = "translateY(0)";
  }, 10);

  // Hide notification after 3 seconds
  setTimeout(() => {
    notification.style.opacity = "0";
    notification.style.transform = "translateY(-20px)";
  }, 3000);
}
